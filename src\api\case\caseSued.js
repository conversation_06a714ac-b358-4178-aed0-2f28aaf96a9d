import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/casesued/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/casesued/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/casesued/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/casesued/queryById',
            method: 'post',
            data
        })
    },
    queryPageDataForDialog(data) {
        return request({
            url: '/casesued/queryPageDataForDialog',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/casesued/setParam',
            method: 'post',
            data
        })
    }
}
