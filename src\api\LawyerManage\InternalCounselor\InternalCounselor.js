import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/internalCounselor/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/internalCounselor/save',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/internalCounselor/queryById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/internalCounselor/delete',
            method: 'post',
            data
        })
    },
    exportCaseLedger(data) {
        return request({
            url: '/internalCounselor/exportCaseLedger',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    queryStaffInfoById(data) {
        return request({
            url: '/internalCounselor/queryStaffInfoById',
            method: 'post',
            data
        })
    }
}

