import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawFirmOutApprovalDetail/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawFirmOutApprovalDetail/save',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/lawFirmOutApprovalDetail/queryDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/lawFirmOutApprovalDetail/delete',
            method: 'post',
            data
        })
    }
}
