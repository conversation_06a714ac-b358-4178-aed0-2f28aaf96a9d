import {request} from '@/api';


export default {
  query(data) {
    return request.post('/contractReport/query', data);
  },

  queryType(data) {
    return request.post('/contractReport/queryType', data);
  },

  queryUnit(data) {
    return request.post('/contractReport/queryUnit', data);
  },

  queryMain(data) {
    return request.post('/contractReport/queryMain', data);
  },

  exportTableExcel(data) {
    return request.post('/contractReport/exportTable', data, {responseType: 'blob'});
  },

  exportTypeExcel(data) {
    return request.post('/contractReport/exportTypeExcel', data, {responseType: 'blob'});
  },

  exportEchartsExcel(data) {
    return request.post('/contractReport/exportEcharts', data, {responseType: 'blob'});
  },

  exportTypeReport(data) {
    return request.post('/contractReport/exportTypeReport', data, {responseType: 'blob'});
  },
}



