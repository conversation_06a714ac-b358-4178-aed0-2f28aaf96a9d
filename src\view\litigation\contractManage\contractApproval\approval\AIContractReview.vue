<template>
  <div class="ai-contract-review">
    <!-- 美化的加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">AI正在分析合同内容...</div>
        <div class="loading-subtitle">请稍候，这可能需要几秒钟</div>
      </div>
    </div>

    <el-row :gutter="24" class="main-content">
      <!-- 左侧：合同文本显示区域 -->
      <el-col :span="12">
        <el-card class="text-card" shadow="hover">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-document"></i>
              <span class="card-title">合同文本</span>
            </div>
            <div class="header-right">
              <el-tag v-if="currentFileInfo" size="small" type="success" style="margin-right: 8px;">
                {{ currentFileInfo }}
              </el-tag>
              <el-tag size="small" type="info">{{ form.complianceText ? form.complianceText.length : 0 }} 字符</el-tag>
            </div>
          </div>

          <div class="text-content">
            <!-- 文件选择区域 -->
            <div v-if="availableFiles.length > 1" class="file-selector">
              <el-select
                v-model="selectedFileIndex"
                @change="onFileChange"
                placeholder="选择要分析的合同文件"
                style="width: 100%; margin-bottom: 16px;">
                <el-option
                  v-for="(file, index) in availableFiles"
                  :key="index"
                  :label="file.name"
                  :value="index">
                </el-option>
              </el-select>
            </div>

            <div id="markdown-preview" class="markdown-preview enhanced">
              {{ complianceTextWithDefault }}
            </div>

            <div class="action-bar">
              <!-- UploadDoc组件方法按钮组 -->
              <div class="upload-doc-methods" style="margin-bottom: 16px;">
                <el-button-group>
                  <el-button
                    size="small"
                    @click="downloadCurrentFile"
                    :disabled="!currentFileInfo"
                    icon="el-icon-download">
                    下载文件
                  </el-button>
                  <el-button
                    size="small"
                    @click="previewCurrentFile"
                    :disabled="!currentFileInfo"
                    icon="el-icon-view">
                    预览文件
                  </el-button>
                 <!-- <el-button
                    size="small"
                    @click="convertCurrentFileToPDF"
                    :disabled="!currentFileInfo"
                    icon="el-icon-document">
                    转换PDF
                  </el-button> -->
                </el-button-group>
              </div>

              <!-- 主要操作按钮 -->
              <el-button
                type="primary"
                @click="submitForm"
                :loading="isLoading"
                size="medium"
                class="submit-btn">
                <i class="el-icon-cpu"></i>
                {{ isLoading ? '分析中...' : '开始AI审查' }}
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：审查结果显示区域 -->
      <el-col :span="12">
        <el-card class="result-card" shadow="hover">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-view"></i>
              <span class="card-title">审查结果</span>
            </div>
            <div class="header-right">
              <el-radio-group v-model="activeParty" class="stance-selector" size="small">
                <el-radio-button label="all">
                  <i class="el-icon-s-grid"></i>
                  全部立场
                </el-radio-button>
                <el-radio-button label="partyA">
                  <i class="el-icon-user"></i>
                  甲方立场
                </el-radio-button>
                <el-radio-button label="partyB">
                  <i class="el-icon-user-solid"></i>
                  乙方立场
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="result-content">
            <div class="markdown-container enhanced" @mouseup="handleTextSelection">
              <div id="markdown-result" class="markdown-content enhanced" v-html="renderedMarkdown"></div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 美化的匹配导航器 -->
    <div v-if="matches.length > 0" class="match-navigator enhanced">
      <div class="navigator-content">
        <el-button
          @click="scrollToPrevMatch"
          size="small"
          circle
          icon="el-icon-arrow-up"
          class="nav-btn">
        </el-button>
        <span class="match-info">
          <strong>{{ currentMatch + 1 }}</strong> / {{ matches.length }}
        </span>
        <el-button
          @click="scrollToNextMatch"
          size="small"
          circle
          icon="el-icon-arrow-down"
          class="nav-btn">
        </el-button>
      </div>
      <div class="navigator-label">匹配导航</div>
    </div>
  </div>
</template>

<script>
import showdown from 'showdown'
import mammoth from 'mammoth'
import docApi from '@/api/_system/doc'

export default {
  name: 'AIContractReview',
  props: {
    contractFiles: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: { 
        complianceText: "" 
      },
      matches: [],
      currentMatch: 0,
      uploadedFile: null,
      activeParty: 'all',
      response: { 
        all: `## 
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交"按钮以获取审查内容。
2. 使用鼠标选中需要匹配的文本，系统将自动高亮并显示匹配结果。`, 
        partyA: `## 
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交"按钮以获取审查内容。
2. 使用鼠标选中需要匹配的文本，系统将自动高亮并显示匹配结果。`, 
        partyB: `## 
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交"按钮以获取审查内容。
2. 使用鼠标选中需要匹配的文本，系统将自动高亮并显示匹配结果。`, 
      },
      fjid: null, // 文件id
      isLoading: false, // 加载状态
      currentFileInfo: null, // 当前使用的文件信息
      selectedFileIndex: 0 // 选中的文件索引
    }
  },
  computed: {
    complianceTextWithDefault() {
      return this.form.complianceText || '解析文本中......';
    },
    renderedMarkdown() {
      const converter = new showdown.Converter();
      return converter.makeHtml(this.response[this.activeParty]);
    },
    // 可用文件列表
    availableFiles() {
      const files = [];
      if (this.contractFiles && this.contractFiles.length > 0) {
        this.contractFiles.forEach((item, index) => {
          if (item) {
            // 处理新的数据格式：直接包含docId, name, status的对象
            if (item.docId && item.name) {
              files.push({
                name: item.name,
                id: item.docId,
                status: item.status,
                originalIndex: index,
                data: item
              });
            }
            // 处理嵌套在data对象中的docId格式
            else if (item.data && item.data.docId && item.data.name) {
              files.push({
                name: item.data.name,
                id: item.data.docId,
                status: item.data.status,
                uid: item.data.uid,
                isVideo: item.data.isVideo,
                originalIndex: index,
                data: item.data
              });
            }
            // 兼容旧的attachment格式
            else if (item.attachment) {
              try {
                const attachmentData = JSON.parse(item.attachment);
                if (attachmentData && attachmentData.length > 0) {
                  attachmentData.forEach((file, fileIndex) => {
                    files.push({
                      name: file.fileName || file.name || `合同文件${index + 1}-${fileIndex + 1}`,
                      id: file.id || file.fileId || file.fjid || file.docId,
                      originalIndex: index,
                      fileIndex: fileIndex,
                      data: file
                    });
                  });
                }
              } catch (e) {
                console.warn('解析attachment数据失败:', e);
              }
            }
            // 处理直接包含文件信息的格式（兼容更多情况）
            else if (item.name && (item.id || item.docId || item.fileId)) {
              files.push({
                name: item.name,
                id: item.id || item.docId || item.fileId,
                status: item.status,
                originalIndex: index,
                data: item
              });
            }
          }
        });
      }
      console.log('解析后的可用文件列表:', files);
      return files;
    }
  },
  mounted() {
    this.fetchComplianceText(); // 页面加载时调用接口获取文本
  },
  methods: {
    // 使用UploadDoc组件的方法获取文本内容
    fetchComplianceTextFromUploadDoc() {
      this.isLoading = true;

      // 获取选中的文件
      if (this.availableFiles.length === 0) {
        this.$message.error('未找到合同文件，无法获取文本内容');
        this.isLoading = false;
        return;
      }

      const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
      const docId = selectedFile.id; // 这里应该是从data.docId提取的值
      this.currentFileInfo = selectedFile.name;

      console.log('选中的文件信息:', selectedFile);
      console.log('使用的docId:', docId);
      console.log('availableFiles列表:', this.availableFiles);
      console.log('selectedFileIndex:', this.selectedFileIndex);

      // 额外的调试信息
      if (!docId) {
        console.error('docId为空！检查数据结构:');
        console.log('原始contractFiles:', this.contractFiles);
        console.log('当前selectedFile:', selectedFile);
      }

      // 使用UploadDoc组件的docApi下载文件并提取文本
      docApi.download(docId).then(response => {
        // 获取文件blob
        const blob = response.data;
        const fileName = selectedFile.name;

        console.log('文件下载成功，文件大小:', blob.size);

        // 根据文件类型处理文本提取
        this.extractTextFromBlob(blob, fileName, docId);

      }).catch(error => {
        console.error('下载文件失败:', error);
        this.$message.warning('UploadDoc下载失败，尝试使用原有接口解析');
        // 如果下载失败，尝试使用原有接口
        this.fetchComplianceTextFromOriginalAPI(docId);
      });
    },

    // 从blob中提取文本内容
    extractTextFromBlob(blob, fileName, docId) {
      const fileExtension = fileName.split('.').pop().toLowerCase();

      if (fileExtension === 'txt') {
        // 处理文本文件
        this.readTextFile(blob);
      } else if (fileExtension === 'pdf') {
        // 对于PDF文件，通过接口请求获取文本
        this.sendFileToAPI(docId);
      } else if (['doc', 'docx'].includes(fileExtension)) {
        // 对于Word文件，直接展示文本内容
        this.readWordFile(blob, fileName);
      } else {
        // 其他文件类型，尝试发送文件到API
        this.sendFileToAPI(docId);
      }
    },

    // 读取文本文件内容
    readTextFile(blob) {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.form.complianceText = e.target.result;
        this.isLoading = false;
        this.$message.success('文本内容加载成功');
      };
      reader.onerror = () => {
        this.$message.error('读取文件内容失败');
        this.isLoading = false;
      };
      reader.readAsText(blob, 'UTF-8');
    },

    // 读取Word文件内容
    readWordFile(blob, fileName) {
      console.log('处理Word文档:', fileName);

      // 使用mammoth.js来提取Word文档的文本内容
      const fileExtension = fileName.split('.').pop().toLowerCase();

      if (fileExtension === 'docx') {
        // 处理 .docx 文件
        mammoth.extractRawText({arrayBuffer: blob})
          .then((result) => {
            const text = result.value; // 提取的纯文本
            if (text && text.trim().length > 0) {
              this.form.complianceText = text.trim();
              this.isLoading = false;
              this.$message.success('Word文档文本提取成功');
              console.log('提取的文本长度:', text.length);
            } else {
              this.$message.warning('Word文档中未找到文本内容');
              this.form.complianceText = '文档中未找到可读取的文本内容';
              this.isLoading = false;
            }
          })
          .catch((error) => {
            console.error('Word文档解析失败:', error);
            this.$message.warning('Word文档解析失败，尝试使用API接口');
            // 降级到API接口
            this.sendFileToAPI(this.getCurrentDocId());
          });
      } else if (fileExtension === 'doc') {
        // 对于 .doc 文件，mammoth.js 支持有限，建议使用API接口
        this.$message.info('检测到.doc格式文件，正在通过API解析...');
        this.sendFileToAPI(this.getCurrentDocId());
      } else {
        // 其他情况，使用API接口
        this.sendFileToAPI(this.getCurrentDocId());
      }
    },

    // 获取当前文档ID的辅助方法
    getCurrentDocId() {
      const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
      return selectedFile ? selectedFile.id : null;
    },

    // 调用原有接口获取文本（作为备用方案）
    fetchComplianceTextFromOriginalAPI(docId) {
      console.log('使用原有接口解析文件，docId:', docId);

      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      // 根据实际的接口参数要求，可能需要使用docId而不是fjid
      const raw = JSON.stringify({
        "fjid": docId  // 或者根据接口文档使用 "docId": docId
      });

      const requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
      };

      fetch("http://172.16.31.234:5004/mobilemode/api/htsc/htsc02/api_hgsc", requestOptions)
        .then(response => response.json())
        .then(result => {
          console.log('原有接口返回结果:', result);
          const wenbeng = result.wenben;
          if (wenbeng) {
            this.form.complianceText = wenbeng;
            this.$message.success('文本内容解析成功');
          } else {
            this.$message.warning('接口返回的文本内容为空');
            this.form.complianceText = '未能获取到文本内容';
          }
          this.isLoading = false;
        })
        .catch(error => {
          console.error('原有接口调用失败:', error);
          this.$message.error('获取文本失败，请检查网络或接口');
          this.isLoading = false;
        });
    },

    // 新增方法：将文件作为参数传递给API
    sendFileToAPI(docId) {
      console.log('将文件传递给API，docId:', docId);

      // 首先下载文件
      docApi.download(docId).then(response => {
        const blob = response.data;
        const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
        const fileName = selectedFile.name;

        console.log('文件下载成功，准备发送到API，文件大小:', blob.size);

        // 创建FormData对象来发送文件
        const formData = new FormData();
        formData.append('file', blob, fileName);
        formData.append('fjid', docId);

        // 发送文件到API
        const requestOptions = {
          method: 'POST',
          body: formData,
          redirect: 'follow'
        };

        fetch("http://172.16.31.234:5004/mobilemode/api/htsc/htsc02/api_hgsc", requestOptions)
          .then(response => response.json())
          .then(result => {
            console.log('文件发送API返回结果:', result);
            const wenbeng = result.wenben;
            if (wenbeng) {
              this.form.complianceText = wenbeng;
              this.$message.success('文件处理成功，文本内容已获取');
            } else {
              this.$message.warning('API返回的文本内容为空');
              this.form.complianceText = '未能获取到文本内容';
            }
            this.isLoading = false;
          })
          .catch(error => {
            console.error('文件发送API失败:', error);
            this.$message.error('文件处理失败，请检查网络或接口');
            // 如果文件发送失败，尝试使用原有的JSON方式
            this.fetchComplianceTextFromOriginalAPI(docId);
          });

      }).catch(error => {
        console.error('下载文件失败:', error);
        this.$message.warning('文件下载失败，尝试使用原有接口解析');
        // 如果下载失败，尝试使用原有接口
        this.fetchComplianceTextFromOriginalAPI(docId);
      });
    },

    // 使用UploadDoc组件的预览方法获取文件路径
    getFilePreviewPath(docId, orgId) {
      return docApi.getFilePath(docId, orgId);
    },

    // 使用UploadDoc组件的编辑方法获取文件路径
    getFileEditPath(docId, orgId) {
      return docApi.getEditFilePath(docId, orgId);
    },

    // 使用UploadDoc组件的PDF转换方法
    convertFileToPDF(docId) {
      return docApi.convertToPDF(docId);
    },

    // 获取当前组织ID（从UploadDoc组件的逻辑中借鉴）
    getCurrentOrgId() {
      // 这里需要从vuex或其他地方获取当前组织ID
      // 模拟获取组织ID的逻辑
      return this.$store?.getters?.orgContext?.currentOrgId ||
             this.$store?.getters?.orgContext?.currentOgnId ||
             'default-org-id';
    },

    // 下载当前选中的文件（使用UploadDoc组件方法）
    downloadCurrentFile() {
      if (this.availableFiles.length === 0) {
        this.$message.warning('没有可下载的文件');
        return;
      }

      const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
      const docId = selectedFile.id;
      const fileName = selectedFile.name;

      this.$message.info('正在下载文件...');

      docApi.download(docId).then(response => {
        // 创建下载链接
        const blob = new Blob([response.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('文件下载成功');
      }).catch(error => {
        console.error('下载文件失败:', error);
        this.$message.error('下载文件失败');
      });
    },

    // 预览当前选中的文件（使用UploadDoc组件方法）
    previewCurrentFile() {
      if (this.availableFiles.length === 0) {
        this.$message.warning('没有可预览的文件');
        return;
      }

      const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
      const docId = selectedFile.id;
      const orgId = this.getCurrentOrgId();

      try {
        const previewPath = this.getFilePreviewPath(docId, orgId);
        // 在新窗口中打开预览
        window.open(previewPath, '_blank');
        this.$message.success('正在打开文件预览');
      } catch (error) {
        console.error('预览文件失败:', error);
        this.$message.error('预览文件失败');
      }
    },

    // 转换当前文件为PDF（使用UploadDoc组件方法）
    convertCurrentFileToPDF() {
      if (this.availableFiles.length === 0) {
        this.$message.warning('没有可转换的文件');
        return;
      }

      const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
      const docId = selectedFile.id;

      this.$message.info('正在转换为PDF...');

      this.convertFileToPDF(docId).then(response => {
        this.$message.success('PDF转换成功');
        // 可以在这里处理转换后的PDF文件
        console.log('PDF转换结果:', response);
      }).catch(error => {
        console.error('PDF转换失败:', error);
        this.$message.error('PDF转换失败');
      });
    },



    // 主要的获取文本方法（优先发送文件到API）
    fetchComplianceText() {
      // 获取选中的文件
      if (this.availableFiles.length === 0) {
        this.$message.error('未找到合同文件，无法获取文本内容');
        return;
      }

      const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
      const docId = selectedFile.id;
      this.currentFileInfo = selectedFile.name;

      console.log('开始处理文件，选中的文件信息:', selectedFile);
      console.log('使用的docId:', docId);

      if (!docId) {
        console.error('docId为空！检查数据结构:');
        console.log('原始contractFiles:', this.contractFiles);
        console.log('当前selectedFile:', selectedFile);
        this.$message.error('文件ID无效，无法处理文件');
        return;
      }

      this.isLoading = true;

      // 优先尝试将文件发送到API进行处理
      this.sendFileToAPI(docId);
    },

    // 文件切换方法
    onFileChange(index) {
      this.selectedFileIndex = index;
      // 重新获取文本内容
      this.fetchComplianceText();
    },

    // 添加导航方法
    scrollToNextMatch() {
      const nextIndex = (this.currentMatch + 1) % this.matches.length;
      this.scrollToMatch(nextIndex);
    },
    scrollToPrevMatch() {
      const prevIndex = (this.currentMatch - 1 + this.matches.length) % this.matches.length;
      this.scrollToMatch(prevIndex);
    },
    
    submitForm() {
      // 检查 complianceText 是否为空
      if (!this.form.complianceText.trim()) {
        this.$message.error('解析失败，请检查文本是否为空');
        return;
      }

      // 创建 Headers 对象
      this.isLoading = true; // 开始加载
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      // 将 <p> 和 </p> 标签替换为换行符
      let plainText = this.form.complianceText
        .replace(/(\r\n|\n|\r)/gm, '\n') // 统一换行符为 \n
        .replace(/^\s+|\s+$/gm, '');

      // 替换连续的换行符为一个换行符
      plainText = plainText.replace(/\n+/g, '\n');

      // 根据 activeParty 设置 shencha_moshi
      let shencha_moshi = '';
      if (this.activeParty === 'all') {
        shencha_moshi = 'quan';
      } else if (this.activeParty === 'partyA') {
        shencha_moshi = 'jia';
      } else if (this.activeParty === 'partyB') {
        shencha_moshi = 'yi';
      }

      // 准备请求体
      const raw = JSON.stringify({
        "messages": [
          {
            "role": "user",
            "content": plainText,
          }
        ],
        "shencha_moshi": shencha_moshi
      });

      // 请求选项
      const requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
      };

      // 发起请求
      fetch("http://172.16.31.234:5001/v1/chat/completions", requestOptions)
        .then(response => response.json())
        .then(data => {
          if (data.choices && data.choices.length > 0) {
            const message = data.choices[0].message.content;
            // 根据 activeParty 更新 response
            if (this.activeParty === 'all') {
              this.response.all = message;
            } else if (this.activeParty === 'partyA') {
              this.response.partyA = message;
            } else if (this.activeParty === 'partyB') {
              this.response.partyB = message;
            }
          } else {
            console.error('API 响应中没有 choices 数据');
            this.$message.error('API 响应中没有找到有效的回复');
          }
          this.isLoading = false; // 结束加载
        })
        .catch(error => {
          console.error('请求失败', error);
          this.$message.error('请求失败，请检查网络或接口');
          this.isLoading = false; // 结束加载
        });
    },
    
    handleTextSelection() {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();

      if (selectedText) {
        // 使用存储的文件内容进行匹配
        const content = this.form.complianceText;

        // 清除旧高亮和匹配记录
        const leftContent = document.querySelector('.markdown-preview');
        this.clearExistingHighlights(leftContent);
        this.matches = [];
        this.currentMatch = 0;

        // 创建正则表达式（处理特殊字符）
        const escapedText = selectedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedText})`, 'g');

        // 使用文档片段批量操作
        const fragment = document.createDocumentFragment();
        let lastIndex = 0;
        let match;

        while ((match = regex.exec(content)) !== null) {
          const startPos = match.index;
          const endPos = startPos + match[0].length;

          // 添加前半部分文本（包含换行）
          fragment.appendChild(
            document.createTextNode(content.slice(lastIndex, startPos))
          );

          // 创建高亮节点
          const highlightNode = document.createElement('mark');
          highlightNode.textContent = match[0];
          fragment.appendChild(highlightNode);

          // 记录匹配项
          this.matches.push(highlightNode);

          lastIndex = regex.lastIndex;
        }

        // 添加剩余文本（包含换行）
        fragment.appendChild(
          document.createTextNode(content.slice(lastIndex))
        );

        // 清空 markdown-preview 内容并添加新的片段
        leftContent.innerHTML = '';
        leftContent.appendChild(fragment);

        // 显示匹配信息
        if (this.matches.length > 0) {
          this.$message({
            message: `找到 ${this.matches.length} 处匹配`,
            type: 'info',
            duration: 3000
          });

          // 滚动到第一个匹配项
          this.scrollToMatch(0);

          // 5秒后淡出高亮
          setTimeout(() => {
            this.matches.forEach(node => {
              node.style.backgroundColor = 'transparent';
            });
          }, 5000);
        }
      }
    },

    // 辅助方法：清除现有高亮
    clearExistingHighlights(container) {
      const highlights = container.querySelectorAll('mark');
      highlights.forEach(highlight => {
        const text = highlight.textContent;
        const textNode = document.createTextNode(text);
        highlight.parentNode.replaceChild(textNode, highlight);
      });

      // 合并相邻的文本节点
      this.mergeAdjacentTextNodes(container);
    },

    // 辅助方法：合并相邻的文本节点
    mergeAdjacentTextNodes(element) {
      let node = element.firstChild;
      while (node && node.nextSibling) {
        if (node.nodeType === Node.TEXT_NODE &&
            node.nextSibling.nodeType === Node.TEXT_NODE) {

          const endsWithNewline = /\n$/.test(node.nodeValue);
          const startsWithNewline = /^\n/.test(node.nextSibling.nodeValue);

          // 仅合并不包含换行的相邻节点
          if (!endsWithNewline && !startsWithNewline) {
            node.nodeValue += node.nextSibling.nodeValue;
            node.parentNode.removeChild(node.nextSibling);
          } else {
            node = node.nextSibling;
          }
        } else {
          node = node.nextSibling;
        }
      }
    },
    
    // 新增辅助方法
    scrollToMatch(index) {
      const highlightColor = '#ff000033';
      if (index >= 0 && index < this.matches.length) {
        this.currentMatch = index;
        this.matches[index].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 添加临时高亮效果
        this.matches.forEach(node => node.style.backgroundColor = highlightColor);
        this.matches[index].style.backgroundColor = '#ff0000aa';

        // 触发动画
        this.matches[index].classList.add('text-highlight');
      }
    }
  }
}
</script>

<style scoped>
/* 主容器样式 */
.ai-contract-review {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  /* min-height: calc(100vh); */
}


/* 主内容区域 */
.main-content {
  margin-top: 0;
  display: flex;
  align-items: stretch; /* 使子元素等高 */
  min-height: calc(100vh - 200px); /* 设置最小高度，减去头部和边距 */
}

.main-content .el-col {
  display: flex;
  flex-direction: column;
}

/* 卡片通用样式 */
.text-card, .result-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: 100%; /* 使卡片填满父容器高度 */
  display: flex;
  flex-direction: column;
}

.text-card:hover, .result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e9ecef;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left i {
  font-size: 18px;
  color: #409eff;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 文本内容区域 */
.text-content {
  padding: 24px;
  flex: 1; /* 使内容区域填满剩余空间 */
  display: flex;
  flex-direction: column;
}

/* 文件选择器样式 */
.file-selector {
  margin-bottom: 16px;
}

.file-selector .el-select {
  width: 100%;
}

.file-selector .el-select .el-input__inner {
  border-radius: 6px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.file-selector .el-select .el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.markdown-preview.enhanced {
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #2c3e50;
  white-space: pre-line;
  overflow-y: auto;
  flex: 1; /* 自适应高度，填满剩余空间 */
  min-height: 450px; /* 最小高度 */
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.action-bar {
  margin-top: 20px;
  text-align: center;
}

.submit-btn {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 结果内容区域 */
.result-content {
  padding: 24px;
  flex: 1; /* 使内容区域填满剩余空间 */
  display: flex;
  flex-direction: column;
}

.stance-selector {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stance-selector .el-radio-button__inner {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  transition: all 0.3s ease;
}

.stance-selector .el-radio-button__inner:hover {
  background: #e9ecef;
  color: #495057;
}

.stance-selector .el-radio-button.is-active .el-radio-button__inner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.markdown-container.enhanced {
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  flex: 1; /* 自适应高度，填满剩余空间 */
  min-height: 450px; /* 最小高度 */
  overflow-y: auto;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.markdown-content.enhanced {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  color: #2c3e50;
}

/* Markdown 内容样式美化 */
.markdown-content.enhanced h1,
.markdown-content.enhanced h2,
.markdown-content.enhanced h3 {
  color: #2c3e50;
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
}

.markdown-content.enhanced h2 {
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.markdown-content.enhanced ul,
.markdown-content.enhanced ol {
  padding-left: 20px;
  margin: 16px 0;
}

.markdown-content.enhanced li {
  margin: 8px 0;
  line-height: 1.6;
}

.markdown-content.enhanced strong {
  color: #495057;
  font-weight: 600;
}

.markdown-content.enhanced code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  color: #e83e8c;
}

/* 美化的匹配导航器 */
.match-navigator.enhanced {
  position: fixed;
  right: 30px;
  bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  z-index: 2000;
  overflow: hidden;
  transition: all 0.3s ease;
}

.match-navigator.enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

.navigator-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.match-info {
  color: white;
  font-size: 14px;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

.navigator-label {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 12px;
  text-align: center;
  padding: 6px 0;
  font-weight: 500;
}

/* 美化的加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loading-subtitle {
  font-size: 14px;
  opacity: 0.8;
  font-weight: 300;
}

/* 高亮动画 */
@keyframes highlight-pulse {
  0% { background-color: rgba(255, 193, 7, 0.8); }
  50% { background-color: rgba(255, 193, 7, 0.4); }
  100% { background-color: rgba(255, 193, 7, 0.2); }
}

.text-highlight {
  animation: highlight-pulse 2s ease-in-out;
  border-radius: 4px;
  padding: 2px 4px;
  transition: all 0.3s ease;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-contract-review {
    padding: 10px;
  }

  .main-content {
    flex-direction: column; /* 移动端垂直排列 */
    min-height: auto; /* 移动端不限制最小高度 */
  }

  .main-content .el-col {
    margin-bottom: 20px;
  }

  .card-header {
    padding: 16px 20px;
  }

  .text-content,
  .result-content {
    padding: 20px;
  }

  .markdown-preview.enhanced,
  .markdown-container.enhanced {
    min-height: 250px; /* 移动端减小最小高度 */
    flex: none; /* 移动端不使用flex自适应 */
    height: 300px; /* 移动端固定高度 */
  }
}
</style>
