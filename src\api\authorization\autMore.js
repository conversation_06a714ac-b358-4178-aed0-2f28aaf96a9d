import {request} from '@/api/index'

export default {
    save(data) {
        return request({
            url: '/AutMoreApi/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/AutMoreApi/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/AutMoreApi/queryById',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/AutMoreApi/setParam',
            method: 'post',
            data
        })
    }
}