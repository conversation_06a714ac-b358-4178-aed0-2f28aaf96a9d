import {request} from '@/api/index';

export default{
    updateById(data){
        return request({
            url:'/OrganizationMember/updateById',
            method:'post',
            data
        })
    },
    //查询指定人员信息
    queryById(data){
        return request({
            url: '/OrganizationMember/queryById/' + data,
            method: 'get'
        })
    },
    // 合规人员列表
    query(data){
        return request({
            url:'/OrganizationMember/query',
            method:'post',
            data
        })
    },
    // 选择人员弹窗
    queryDia(data){
        return request({
            url:'/OrganizationMember/queryDia',
            method:'post',
            data
        })
    },
    // 查询历史
    queryHistory(data){
        return request({
            url:'/OrganizationMember/queryHistory',
            method:'post',
            data
        })
    },
    // 删除人员
    delete(data){
        return request({
            url:'/OrganizationMember/delete',
            method:'post',
            data
        })
    },
    // 启用
    update(data){
        return request({
            url:'/OrganizationMember/update',
            method:'post',
            data
        })
    },
    
    // 组织角色列表
    queryRole(data){
        return request({
            url:'/OrganizationRole/query',
            method:'post',
            data
        })
    },

    save(data){
        return request({
            url:'/OrganizationMember/save',
            method:'post',
            data
        })
    },

    deleteNode(data){
        return request({
            url:'/OrganizationRole/delete',
            method:'post',
            data
        })
    },

    saveRole(data){
        return request({
            url:'/OrganizationRole/save',
            method:'post',
            data
        })
    }
}