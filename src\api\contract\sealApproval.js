import { request } from '@/api/index';

export default {
	query(data) {
		return request({
			url: '/sealapproval/query',
			method: 'post',
			data,
		});
	},
	queryByContractId(data) {
		return request({
			url: '/sealapproval/queryByContractId',
			method: 'post',
			data,
		});
	},
    queryExistenceByContractId(data) {
        return request({
            url: '/sealapproval/queryExistenceByContractId',
            method: 'post',
            data,
        });
    },
	getAutSealList(data) {
		return request({
			url: '/sealapproval/getAutSealList',
			method: 'post',
			data,
		});
	},
	querySealDetailList(data) {
		return request({
			url: '/sealapproval/querySealDetailList',
			method: 'post',
			data,
		});
	},
	save(data) {
		return request({
			url: '/sealapproval/save',
			method: 'post',
			data,
		});
	},
	delete(data) {
		return request({
			url: '/sealapproval/delete',
			method: 'post',
			data,
		});
	},
	queryById(data) {
		return request({
			url: '/sealapproval/queryById',
			method: 'post',
			data,
		});
	},
	sxqueryById(data) {
		return request({
			url: '/sealapproval/sxqueryById',
			method: 'post',
			data,
		});
	},
	setParam(data) {
		return request({
			url: '/sealapproval/setParam',
			method: 'post',
			data,
		});
	},
	loginSeal(data){
		return request({
			url: '/sealapproval/loginSeal',
			method: 'post',
			data
		});
	},
	pushElectronicSign(data){
		return request({
			url: '/sealapproval/pushElectronicSign',
			method: 'post',
			data
		});
	},
	pushSignRevoke(data){
		return request({
			url: '/sealapproval/pushSignRevoke',
			method: 'post',
			data
		});
	},
};
