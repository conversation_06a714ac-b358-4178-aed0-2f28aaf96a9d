import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/businesstype/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/businesstype/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/businesstype/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/businesstype/queryById',
      method: 'post',
      data
    })
  },
  checkOnly(data) {
    return request({
      url: '/businesstype/checkOnly',
      method: 'post',
      data
    })
  },
}