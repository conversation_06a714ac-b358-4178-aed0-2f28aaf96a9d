# 岗位职责显示功能修复说明

## 🐛 问题分析

### 原始问题
- 岗位职责信息不显示
- 接口调用正常，但 `currentNodeInfo` 为空

### 根本原因
通过分析真实的接口返回数据，发现数据结构与预期不同：

```javascript
// 真实的接口返回数据结构
{
  "currentNodes": [
    {
      "name": "业务部门负责人",        // ✅ 直接的岗位名称
      "nameId": "Activity_1rmp80v",   // ✅ 岗位对应的nodeId
      "nodeId": "Activity_0u0241c",   // ❓ 另一个nodeId（可能是流程节点）
      // ... 其他字段
    }
  ]
}
```

### 问题所在
1. **数据路径错误**: 原代码使用 `res.data.currentNodes`，实际应该是 `res.currentNodes`
2. **字段选择错误**: 原代码只使用 `nodeId` 进行映射，忽略了直接可用的 `name` 字段
3. **优先级不当**: 没有按照最佳优先级选择字段

## ✅ 修复方案

### 1. 修正数据路径

#### 🔧 修复前
```javascript
if (res && res.data && res.data.currentNodes && res.data.currentNodes.length > 0) {
  const nodeId = res.data.currentNodes[0].nodeId;
  this.currentNodeInfo = this.getNodeNameByNodeId(nodeId);
}
```

#### ✅ 修复后
```javascript
if (res && res.currentNodes && res.currentNodes.length > 0) {
  const currentNode = res.currentNodes[0];
  // 处理逻辑...
}
```

### 2. 优化字段选择逻辑

#### 🎯 新的优先级策略
```javascript
// 优先使用name字段，如果没有则使用nameId映射
if (currentNode.name) {
  this.currentNodeInfo = currentNode.name;           // 第一优先级：直接使用name
} else if (currentNode.nameId) {
  this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nameId);  // 第二优先级：nameId映射
} else if (currentNode.nodeId) {
  this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nodeId);  // 第三优先级：nodeId映射
}
```

### 3. 增强调试信息

#### 🔍 添加详细日志
```javascript
console.log('queryById接口返回数据:', res);
console.log('当前节点信息:', currentNode);
console.log('设置的岗位职责信息:', this.currentNodeInfo);
```

## 📊 字段分析对比

| 字段名 | 示例值 | 用途 | 优先级 |
|--------|--------|------|--------|
| `name` | "业务部门负责人" | 直接的岗位名称 | 🥇 第一优先级 |
| `nameId` | "Activity_1rmp80v" | 岗位对应的nodeId | 🥈 第二优先级 |
| `nodeId` | "Activity_0u0241c" | 流程节点ID | 🥉 第三优先级 |

## 🔧 完整的修复代码

### getCurrentNodeInfo方法
```javascript
getCurrentNodeInfo() {
  if (this.$route.query.id) {
    contractApi.queryById(this.$route.query.id).then(res => {
      console.log('queryById接口返回数据:', res);
      if (res && res.currentNodes && res.currentNodes.length > 0) {
        const currentNode = res.currentNodes[0];
        console.log('当前节点信息:', currentNode);
        
        // 优先使用name字段，如果没有则使用nameId映射
        if (currentNode.name) {
          this.currentNodeInfo = currentNode.name;
        } else if (currentNode.nameId) {
          this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nameId);
        } else if (currentNode.nodeId) {
          this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nodeId);
        }
        
        console.log('设置的岗位职责信息:', this.currentNodeInfo);
      }
    }).catch(error => {
      console.error('获取当前节点信息失败:', error);
    });
  }
}
```

### getNodeNameByNodeId方法
```javascript
getNodeNameByNodeId(nodeId) {
  const nodeMap = {
    'Activity_1rmp80v': '业务部门负责人',
    'Activity_1yc9eu3': '运改部审核',
    'Activity_1upb5zy': '税务审核',
    'Activity_0hgi73c': '资金审核',
    'Activity_0no6qkt': '财务部部长',
    'Activity_1qs8r6i': '法务部风控',
    'Activity_1lee3nx': '办公室',
    'Activity_1umzmjb': '公司领导',
    'Activity_0wn3tir': '返回经办人',
    'Activity_0y3xjh6': '法务承办人',
    'Activity_1e2ebp6': '合同专业负责人',
    'Activity_0pdswu8': '法务部部长',
    'Activity_0pz4x4e': '首席合规官',
    'Activity_1r1du0j': '三级审批',
    'Activity_0u0241c': '三级审批'
  };
  
  return nodeMap[nodeId] || `未知岗位(${nodeId})`;
}
```

## 🧪 测试验证

### 测试用例
1. **name字段存在**: 直接使用name字段值 ✅
2. **name字段为空，nameId存在**: 使用nameId映射 ✅
3. **name和nameId都为空，nodeId存在**: 使用nodeId映射 ✅
4. **所有字段都为空**: 不显示岗位职责行 ✅

### 预期结果
根据您提供的真实数据：
```javascript
{
  "name": "业务部门负责人",
  "nameId": "Activity_1rmp80v",
  "nodeId": "Activity_0u0241c"
}
```

**预期显示**: "业务部门负责人" (直接使用name字段)

## 🎯 修复效果

### 修复前
- ❌ 岗位职责不显示
- ❌ `currentNodeInfo` 为 null
- ❌ 数据路径错误
- ❌ 字段选择不当

### 修复后
- ✅ 岗位职责正常显示
- ✅ `currentNodeInfo` 为 "业务部门负责人"
- ✅ 数据路径正确
- ✅ 字段选择优化

## 🚀 部署说明

### 修改的文件
- `ContractApprovalMain.vue` - 主要修复文件

### 修改的方法
- `getCurrentNodeInfo()` - 数据获取逻辑
- `getNodeNameByNodeId()` - 映射逻辑优化

### 测试方法
1. 打开合同审批页面（带有id参数）
2. 检查页面上方是否显示岗位职责标签
3. 查看浏览器控制台的调试信息
4. 验证显示的岗位名称是否正确

## 📋 注意事项

### 1. 数据结构依赖
- 依赖接口返回 `currentNodes` 数组
- 需要至少一个节点信息
- 字段可能为空，需要降级处理

### 2. 显示逻辑
- 只有获取到岗位信息才显示标签
- 使用 `v-if="currentNodeInfo"` 条件渲染
- 避免显示空白或错误信息

### 3. 错误处理
- 接口调用失败时输出错误日志
- 未知nodeId时显示带ID的提示
- 所有字段为空时不显示标签

## 🎉 总结

通过这次修复：

1. **正确识别数据结构**: 使用 `res.currentNodes` 而不是 `res.data.currentNodes`
2. **优化字段选择策略**: 优先使用 `name` 字段，降级使用映射
3. **增强调试能力**: 添加详细的控制台日志
4. **提升用户体验**: 确保岗位职责信息正确显示

现在岗位职责显示功能应该能够正常工作，用户可以清楚地看到当前审批环节的岗位信息！
