import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/adminconfig/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/adminconfig/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/adminconfig/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/adminconfig/queryById',
            method: 'post',
            data
        })
    }
}
