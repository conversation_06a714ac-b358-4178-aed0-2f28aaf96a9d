import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/contractTemplate/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/contractTemplate/save',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/contractTemplate/queryById',
            method: 'post',
            data
        })
    },
    stopSave(data) {
        return request({
            url: '/contractTemplate/stopSave',
            method: 'post',
            data
        })
    },
    queryStandardAttNoModeList(data) {
        return request({
            url: '/contractTemplate/queryStandardAttNoModeList',
            method: 'post',
            data
        })
    },
}