import {request} from '@/api/index'


export default {

    //根据合同编码 查询对应的履行记录
    queryByContractCode(data) {
        return request({
            url: '/bm-contract-perform-result/queryByContractCode',
            method: 'post',
            data
        })
    },
    //履行记录查询接口
    query(data){
        return request({
            url: '/bm-contract-perform-result/query',
            method: 'post',
            data
        })
    },
    //履行记录查询接口
    cliamResult(data){
        return request({
            url: '/bm-contract-perform-result/cliamResult',
            method: 'post',
            data
        })
    }

}