var zcrUtil = {}

zcrUtil.handleClick = function (_this) {
  _this.loading = true
  if (_this.activeName == 'lishi') {
    // const bsapi = window.getFrameAPI(_this.docId)
    // bsapi.CallFrameAPI("getTrackChanges", function (info) {
    //   _this.trackData = info
    // })
  } else if (_this.activeName == 'shoucang') {
    this.handleSCJlick(_this)
  } else if (_this.activeName == 'shengcha') {
    this.handleSCClick(_this)
  } else if (_this.activeName == 'tuison') {
    this.refreshData(_this)
  } else if (_this.activeName == 'jiansuo') {
    this.getSearchData(_this)
  }
}

// 获取检索首页数据
zcrUtil.getSearchData = function (_this) {
  _this.MOTemp = {
    page: 1,
    limit: 15,
    total: 0
  }
  _this.DETemp = {
    id: null,
    page: 1,
    limit: 10,
    total: 0
  }
  _this.JSTemp = {
    knparam: null,
    page: 1,
    limit: 10,
    total: 0
  }
  axios.post(_this.ajaxpath2 + '/external/knMainSearch', {kntype: '法律法规', page: 1, limit: 5})
      .then(function (response) {
        _this.FGData = []
        _this.FGData = response.data.data.records
        _this.showTable = true
        _this.showList = false
        _this.showDetail = false
        _this.showMore = false
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
  axios.post(_this.ajaxpath2 + '/external/knMainSearch', {kntype: '合同条款', page: 1, limit: 5})
      .then(function (response) {
        _this.TKData = []
        _this.TKData = response.data.data.records
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
  axios.post(_this.ajaxpath2 + '/external/knMainSearch', {kntype: '审查意见', page: 1, limit: 5})
      .then(function (response) {
        _this.YJData = []
        _this.YJData = response.data.data.records
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
  axios.post(_this.ajaxpath2 + '/external/knMainSearch', {kntype: '合同范本', page: 1, limit: 5})
      .then(function (response) {
        _this.FBData = []
        _this.FBData = response.data.data.records
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 收藏夹点击
zcrUtil.handleSCJlick = function (_this) {
  _this.loading = true
  let type = ''
  if (_this.activeSCJ == 'shoucang_bc') {
    type = 'bc'
  } else {
    type = 'lishi'
  }
  axios.post(_this.ajaxpath + '/textCollect/queryDataByPId', {
    id: _this.cUserId,
    type: type,
    parentId: _this.contractId
  })
      .then(function (response) {
        _this.collectData = []
        _this.collectData = response.data.data
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 审查点击
zcrUtil.handleSCClick = function (_this) {
  _this.loading = true

  if (_this.activeSC == 'shengcha_cw') {
    _this.examineData = []
    _this.colorData = []
    _this.warnData = []
    axios.post(_this.ajaxpath + '/textExamine/queryDataByPId', {id: _this.contractId})
        .then(function (response) {
          if (response.data.data) {
            _this.examineData = JSON.parse(response.data.data.examines)
            _this.colorData = JSON.parse(response.data.data.colors)
            _this.warnData = JSON.parse(response.data.data.contents)
            _this.examineSaveData = response.data.data
          } else {
            const bsapi = window.getFrameAPI(_this.docId)
            bsapi.CallFrameAPI("getParagraphInfo", function (info) {
              console.info(info)
              axios.post(_this.ajaxpath2 + '/examineConfig/queryAll', {})
                  .then(function (response) {
                    const tempEData = response.data.page
                    const contentData = info.content.content
                    for (let i = 0; i < contentData.length; i++) {
                      const tempWarn = []
                      let tempex
                      if (contentData[i].content == '\r\n') {
                        continue
                      }
                      for (let j = 0; j < tempEData.length; j++) {
                        if (tempEData[j].examineKey.indexOf('#') > -1) {
                          let tempKey = tempEData[j].examineKey.split('#')
                          let cont = false
                          for (let k = 0; k < tempKey.length; k++) {
                            if (contentData[i].content.indexOf(tempKey[k]) > -1) {
                              cont = true
                              break
                            }
                          }
                          if (cont) {
                            const temp = {
                              paraId: contentData[i].paraId,
                              content: contentData[i].content,
                              checked: false
                            }
                            tempex = tempEData[j]
                            tempWarn.push(temp)
                          }
                        } else {
                          if (contentData[i].content.indexOf(tempEData[j].examineKey) > -1) {
                            const temp = {
                              paraId: contentData[i].paraId,
                              content: contentData[i].content,
                              checked: false
                            }
                            tempex = tempEData[j]
                            tempWarn.push(temp)
                          }
                        }
                      }
                      if (tempWarn.length > 0) {
                        _this.examineData.push(tempex)
                        _this.warnData.push(tempWarn)
                        _this.colorData.push(false)
                      }
                    }
                    _this.examineSaveData.parentId = _this.contractId
                    _this.examineSaveData.id = _this.createUUID()
                    _this.examineSaveData.colors = _this.colorData
                    _this.examineSaveData.contents = _this.warnData
                    _this.examineSaveData.examines = _this.examineData
                    _this.loading = false
                  }).catch(function (error) { // 请求失败处理
                console.log(error);
              })
            })
          }
          for (let i = 0; i < _this.warnData.length; i++) {
            let warn = _this.warnData[i]
            let all = true
            for (let j = 0; j < warn.length; j++) {
              if (!warn[j].checked) {
                all = false
                break
              }
            }
            if (all) {
              _this.checkAll.push(true)
            } else {
              _this.checkAll.push(false)
            }
          }
          let allc = true
          for (let i = 0; i < _this.colorData.length; i++) {
            if (!_this.colorData[i]) {
              allc = false
              break
            }
          }
          if (allc) {
            _this.allAll = true
          } else {
            _this.allAll = false
          }
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
  } else if (_this.activeSC == 'shengcha_yj') {
    let codes = ['TEXT-FXDJ']
    axios.post(_this.ajaxpath + '/sys_dict/queryDataByArray', codes)
        .then(function (response) {
          _this.levelData = response.data.data[codes[0]]
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
    if (_this.cUserId == _this.createPsnId) {
      _this.showResult = true
    }
    if (_this.activeYJ == 'ychuli') {
      axios.post(_this.ajaxpath + '/textComment/queryDataByPId', {id: _this.contractId, deal: true})
          .then(function (response) {
            if (response.data.data.length > 0) {
              _this.commentsYData = response.data.data
              let ids = []
              for (let i = 0; i < _this.commentsYData.length; i++) {
                ids.push(_this.commentsYData[i].id)
              }
              axios.post(_this.ajaxpath + '/textCollect/queryDataByIds', {id: ids})
                  .then(function (response) {
                    _this.hasCollectYData = response.data.data
                  }).catch(function (error) { // 请求失败处理
                console.log(error);
              })
            }
            _this.loading = false
          }).catch(function (error) { // 请求失败处理
        console.log(error);
      })
    } else {
      axios.post(_this.ajaxpath + '/textComment/queryDataByPId', {id: _this.contractId})
          .then(function (response) {
            const bsapi = window.getFrameAPI(_this.docId)
            bsapi.CallFrameAPI("getAllComments", function (data) {
              if (data.content.comments.length > 0) {
                let coData = []
                let cData = data.content.comments
                for (let i = 0; i < cData.length; i++) {
                  let commentData = {
                    id: _this.createUUID(),
                    userName: cData[i].username,
                    userId: null,
                    commentTime: cData[i].time,
                    commentContent: cData[i].quote,
                    commentId: cData[i].id,
                    comments: cData[i].comment,
                    commentLevel: null,
                    commentLevelCode: null,
                    commentLabel: null,
                    commentLabelId: null,
                    whetherDeal: false,
                    whetherAccept: null,
                    reason: null,
                    parentId: _this.contractId,
                    parentName: _this.contractName,
                    createPsnId: _this.createPsnId,
                    createPsnName: _this.createPsnName,
                    createTime: new Date()
                  }
                  coData.push(commentData)
                }
                if (response.data.data.length > 0) {
                  _this.commentsData = response.data.data
                  for (let i = 0; i < coData.length; i++) {
                    let addData = true

                    for (let j = 0; j < _this.commentsData.length; j++) {
                      if (coData[i].commentId == _this.commentsData[j].commentId &&
                          _this.parseTime(coData[i].commentTime) == _this.parseTime(_this.commentsData[j].commentTime) &&
                          coData[i].comments == _this.commentsData[j].comments) {
                        addData = false
                        break
                      } else if (coData[i].commentId == _this.commentsData[j].commentId &&
                          _this.parseTime(coData[i].commentTime) == _this.parseTime(_this.commentsData[j].commentTime) &&
                          coData[i].comments != _this.commentsData[j].comments) {
                        _this.$set(_this.commentsData, j, coData[i])
                        addData = false
                        break
                      } else {
                        addData = true
                      }
                    }
                    if (addData) {
                      _this.commentsData.push(coData[i])
                    }
                  }
                  for (let i = 0; i < _this.commentsData.length; i++) {
                    if (_this.commentsData[i].whetherDeal) {
                      _this.commentsData.splice(i, 1)
                      --i
                    }
                  }
                } else {
                  _this.commentsData = coData
                }
                let ids = []
                for (let i = 0; i < _this.commentsData.length; i++) {
                  ids.push(_this.commentsData[i].id)
                }
                axios.post(_this.ajaxpath + '/textCollect/queryDataByIds', {id: ids})
                    .then(function (response) {
                      _this.hasCollectData = response.data.data
                    }).catch(function (error) { // 请求失败处理
                  console.log(error);
                })
              } else {
                _this.commentsData = []
              }
            })
            _this.loading = false
          }).catch(function (error) { // 请求失败处理
        console.log(error);
      })
    }
  } else if (_this.activeSC == 'shengcha_ys') {

  }
  _this.loading = false
}
zcrUtil.getYSData = function (_this) {
  let codes = ['htlc', 'foldercontract']
  axios.post(_this.ajaxpath2 + '/knManage/queryDataByArray', codes)
      .then(function (response) {
        _this.myPoData = response.data.data[codes[0]]
        _this.conTypeData = response.data.data[codes[1]]
        axios.post(_this.ajaxpath + '/textExtract/queryDataByPId', {id: _this.contractId})
            .then(function (response) {
              if (response.data.data) {
                _this.extractData = response.data.data
              } else {
                _this.extractData.parentId = _this.contractId
                _this.extractData.id = _this.createUUID()
              }
              _this.loading = false
            }).catch(function (error) { // 请求失败处理
          console.log(error);
        })
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 设置已阅颜色样式
zcrUtil.setCheck = function (_this, id, content, index, checked) {
  let read = true
  if (checked) {
    zcrUtil.toGraph(_this, id, content, index, '#FFFFFF')
  } else {
    _this.$set(_this.checkAll, index, false)
    zcrUtil.toGraph(_this, id, content, index, '#FDEDDC')
  }
  for (let i = 0; i < _this.warnData[index].length; i++) {
    if (!_this.warnData[index][i].checked) {
      read = false
      break
    }
  }
  if (read) {
    _this.$set(_this.colorData, index, true)
    _this.$set(_this.checkAll, index, true)
  } else {
    _this.$set(_this.colorData, index, false)
    _this.$set(_this.checkAll, index, false)
  }
}

// 设置高亮并跳转
zcrUtil.toGraph = function (_this, id, content, index, color, checked) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("gotoParagraph", {id: id}, (data) => {
  })
  if (checked) {
    return
  }
  if (_this.examineData[index].examineKey.indexOf('#') > -1) {
    let tempKey = _this.examineData[index].examineKey.split('#')
    for (let k = 0; k < tempKey.length; k++) {
      if (content.indexOf(tempKey[k]) > -1) {
        let indexs = this.getStrPositions(content, tempKey[k]);

        for (let i = 0; i < indexs.length; i++) {
          bsapi.CallFrameAPI("modifyByPos", {
            actions: [{
              "start": {"paraId": id, "index": indexs[i]},
              "end": {"paraId": id, "index": indexs[i] + tempKey[k].length},
              "action": "highlight",
              "content": color
            }]
          }, function (info) {
            console.info(info)
          })
        }
      }
    }
  } else {
    let indexs = this.getStrPositions(content, _this.examineData[index].examineKey);

    for (let i = 0; i < indexs.length; i++) {
      bsapi.CallFrameAPI("modifyByPos", {
        actions: [{
          "start": {"paraId": id, "index": indexs[i]},
          "end": {"paraId": id, "index": indexs[i] + _this.examineData[index].examineKey.length},
          "action": "highlight",
          "content": color
        }]
      }, function (info) {
        console.info(info)
      })
    }
  }
}

// 设置高亮不跳转
zcrUtil.toGraph2 = function (_this, id, content, index, color, checked) {
  const bsapi = window.getFrameAPI(_this.docId)
  if (checked) {
    return
  }
  if (_this.examineData[index].examineKey.indexOf('#') > -1) {
    let tempKey = _this.examineData[index].examineKey.split('#')
    for (let k = 0; k < tempKey.length; k++) {
      if (content.indexOf(tempKey[k]) > -1) {
        let indexs = this.getStrPositions(content, tempKey[k]);

        for (let i = 0; i < indexs.length; i++) {
          bsapi.CallFrameAPI("modifyByPos", {
            actions: [{
              "start": {"paraId": id, "index": indexs[i]},
              "end": {"paraId": id, "index": indexs[i] + tempKey[k].length},
              "action": "highlight",
              "content": color
            }]
          }, function (info) {
            console.info(info)
          })
        }
      }
    }
  } else {
    let indexs = this.getStrPositions(content, _this.examineData[index].examineKey);

    for (let i = 0; i < indexs.length; i++) {
      bsapi.CallFrameAPI("modifyByPos", {
        actions: [{
          "start": {"paraId": id, "index": indexs[i]},
          "end": {"paraId": id, "index": indexs[i] + _this.examineData[index].examineKey.length},
          "action": "highlight",
          "content": color
        }]
      }, function (info) {
        console.info(info)
      })
    }
  }
}

// 编辑批注
zcrUtil.updateComment = function (_this, id, con) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("updateComment", {content: con, id: id})
}

// 删除批注
zcrUtil.deletComment = function (_this, id, id2) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("removeComment", {id: id})
  axios.post(_this.ajaxpath + '/textComment/queryDataById', {id: id2})
      .then(function (response) {
        if (response.data.data) {
          axios.post(_this.ajaxpath + '/textComment/delete', {id: id2})
              .then(function (response) {
                _this.handleSCClick(_this)
              }).catch(function (error) { // 请求失败处理
            console.log(error);
          })
        } else {
          _this.handleSCClick(_this)
        }
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 跳转批注
zcrUtil.goToComment = function (_this, id) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("gotoComment", {id: id})
}

// 自动标签
zcrUtil.autoTag = function (_this) {
  if (!_this.groupId) {
    return
  }
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("getParagraphInfo", function (info) {
    console.info(info)
    axios.post(_this.ajaxpath2 + '/autoConfig/autoBuildEditTag', {
      groupid: _this.groupId,
      content: info.content.content
    })
        .then(function (response) {
          let tempData = response.data.data.labelData
          let tempData2 = response.data.data.labelData2
          if (tempData) {
            for (let i = 0; i < tempData.length; i++) {
              let hasData = false
              let aData = {
                'id': _this.createUUID(),
                'labelName': tempData[i].label,
                'labelId': tempData[i].labelId,
                'contentId': tempData[i].paraId,
                'parentId': _this.contractId,
                'createPsnId': _this.cUserId,
                'createPsnName': _this.cUserName,
                'createTime': new Date()
              }
              if (_this.tagOriData.length > 0) {
                for (let j = 0; j < _this.tagOriData.length; j++) {
                  if (tempData[i].labelId == _this.tagOriData[j].labelId && tempData[i].paraId == _this.tagOriData[j].contentId) {
                    hasData = true
                    break
                  }
                }
                if (!hasData) {
                  _this.tagOriData.push(aData)
                }
              } else {
                _this.tagOriData.push(aData)
              }
            }

            _this.tagData = _this.getNewData(_this, _this.tagOriData)
            let ids = []
            _this.tagIdx = []
            _this.tagSelectData = []
            for (let i = 0; i < _this.tagData.length; i++) {
              ids.push(_this.tagData[i].id)
              _this.tagSelectData.push(false)
              _this.tagIdx.push(0)
            }
            // 查询是否收藏
            axios.post(_this.ajaxpath + '/textCollect/queryDataByIds', {id: ids})
                .then(function (response) {
                  _this.hasTagCollectData = response.data.data
                }).catch(function (error) { // 请求失败处理
              console.log(error);
            })
            // 未匹配数据
            if (tempData2) {
              for (let i = 0; i < tempData2.length; i++) {
                let ids = []
                let aData = {
                  'id': _this.createUUID(),
                  'labelName': tempData2[i].label,
                  'labelId': tempData2[i].labelId,
                  'contentId': null,
                  'parentId': _this.contractId,
                  'createPsnId': _this.cUserId,
                  'createPsnName': _this.cUserName,
                  'createTime': new Date()
                }
                ids.push(aData)
                let sTagPara = {
                  labelId: tempData2[i].labelId,
                  datas: ids
                }
                _this.tagData.push(aData)
                _this.tagOriData.push(aData)
                _this.tagParaId.push(sTagPara)
              }
            }
          }
          _this.loading = false
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
  })
}

// 获取整理后标签数据
zcrUtil.getNewData = function (_this, oldData) {
  _this.tagParaId = []
  // 提取重复标签
  for (let i = 0; i < oldData.length; i++) {
    let sTagPara = {}
    let ids = []
    let hasData = false
    if (_this.tagParaId.length > 0) {
      for (let j = 0; j < _this.tagParaId.length; j++) {
        if (_this.tagParaId[j].labelId == oldData[i].labelId) {
          let tid = _this.tagParaId[j].datas
          tid.push(oldData[i])
          let tempD = {
            labelId: _this.tagParaId[j].labelId,
            datas: tid
          }
          _this.$set(_this.tagParaId, j, tempD)
          hasData = true
          break;
        }
      }
      if (!hasData) {
        ids.push(oldData[i])
        sTagPara = {
          labelId: oldData[i].labelId,
          datas: ids
        }
        _this.tagParaId.push(sTagPara)
      }
    } else {
      ids.push(oldData[i])
      sTagPara = {
        labelId: oldData[i].labelId,
        datas: ids
      }
      _this.tagParaId.push(sTagPara)
    }
  }
  let newData = []
  let hadId = []
  // 处理去除重复数据
  for (let i = 0; i < oldData.length; i++) {
    for (let j = 0; j < _this.tagParaId.length; j++) {
      if (oldData[i].labelId == _this.tagParaId[j].labelId) {
        if (_this.tagParaId[j].datas.length > 1) {
          let hasId = false
          for (let k = 0; k < hadId.length; k++) {
            if (hadId[k] == oldData[i].labelId) {
              hasId = true
              break
            }
          }
          if (!hasId) {
            hadId.push(oldData[i].labelId)
            newData.push(oldData[i])
            break;
          }
        } else {
          newData.push(oldData[i])
          break;
        }
      }
    }
  }
  return newData
}

// 添加标签
zcrUtil.handleChangeItem = function (_this, value) {
  let aData
  const checkedNode = _this.$refs.cascaderArr.getCheckedNodes()
  if (value.length > 0) {
    const bsapi = window.getFrameAPI(_this.docId)
    bsapi.CallFrameAPI("getCurrentParagraph", null, (data) => {
      console.log(data)
      if (data.content.length > 0) {
        let hasData = false
        let newLabel = checkedNode[checkedNode.length - 1].label
        let newValue = value[value.length - 1]
        aData = {
          'id': _this.createUUID(),
          'labelName': newLabel,
          'labelId': newValue,
          'contentId': data.content[0].id,
          'createPsnId': _this.cUserId,
          'createPsnName': _this.cUserName,
          'parentId': _this.contractId,
          'createTime': new Date()
        }
        if (_this.tagOriData.length > 0) {
          for (let j = 0; j < _this.tagOriData.length; j++) {
            if (newValue == _this.tagOriData[j].labelId && data.content[0].id == _this.tagOriData[j].contentId) {
              hasData = true
              break
            }
          }
          if (!hasData) {
            let hasData2 = false
            for (let j = 0; j < _this.tagParaId.length; j++) {
              if (_this.tagParaId[j].labelId == newValue) {
                let tid = _this.tagParaId[j].datas
                tid.push(aData)
                let tempD = {
                  labelId: _this.tagParaId[j].labelId,
                  datas: tid
                }
                _this.$set(_this.tagParaId, j, tempD)
                _this.tagOriData.push(aData)
                hasData2 = true
              }
            }
            if (!hasData2) {
              let da = []
              da.push(aData)
              let sTagPara = {
                labelId: newValue,
                datas: da
              }
              _this.tagParaId.push(sTagPara)
              _this.tagOriData.push(aData)
              _this.tagData.push(aData)
              _this.hasTagCollectData.push(false)
              _this.tagSelectData.push(false)
              _this.tagIdx.push(0)
            }
          }
        } else {
          let da = []
          da.push(aData)
          let sTagPara = {
            labelId: newValue,
            datas: da
          }
          _this.tagParaId.push(sTagPara)
          _this.tagOriData.push(aData)
          _this.tagData.push(aData)
          _this.hasTagCollectData.push(false)
          _this.tagSelectData.push(false)
          _this.tagIdx.push(0)
        }
      } else {
        _this.$message({
          message: '请先选中段落！',
          type: 'warning'
        })
        _this.labelId = null
        return
      }
    })
  }
}

// 设置标签
zcrUtil.setLabel = function (_this, idx) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("getCurrentParagraph", null, (data) => {
    if (data.content.length > 0) {
      let aData = {
        'id': _this.tagData[idx].id,
        'labelName': _this.tagData[idx].labelName,
        'labelId': _this.tagData[idx].labelId,
        'contentId': data.content[0].id,
        'createPsnId': _this.tagData[idx].createPsnId,
        'createPsnName': _this.tagData[idx].createPsnName,
        'parentId': _this.tagData[idx].parentId,
        'createTime': _this.tagData[idx].createTime
      }
      let hasData = false
      for (let i = 0; i < _this.tagParaId.length; i++) {
        if (_this.tagParaId[i].labelId == _this.tagData[idx].labelId) {
          let tid = _this.tagParaId[i].datas
          tid.push(aData)
          let tempD = {
            labelId: _this.tagParaId[i].labelId,
            datas: tid
          }
          _this.$set(_this.tagParaId, i, tempD)
          hasData = true
        }
      }
      if (!hasData) {
        let da = []
        da.push(aData)
        let sTagPara = {
          labelId: _this.tagData[idx].labelId,
          datas: da
        }
        _this.tagParaId.push(sTagPara)
        _this.tagIdx.push(0)
        _this.tagSelectData.push(false)
        _this.hasTagCollectData.push(false)
        _this.$set(_this.tagData, idx, aData)
      }
      for (let i = 0; i < _this.tagOriData.length; i++) {
        if (_this.tagOriData[i].id == _this.tagData[idx].id) {
          _this.$set(_this.tagOriData, i, aData)
        }
      }
    } else {
      _this.$message({
        message: '请先选中段落！',
        type: 'warning'
      })
    }
  })
}

// 跳转标签
zcrUtil.goToLabel = function (_this, id) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("gotoParagraph", {id: id})
}

// 跳转标签
zcrUtil.goToLabel2 = function (_this, id, name) {
  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("gotoParagraph", {id: id})
  if (_this.temp.item != null && _this.temp.item == name) {
    return
  }
  if (_this.contractTypeName != null) {
    _this.temp.item = name + ',' + _this.contractTypeName
  } else {
    _this.temp.item = name
  }
  this.refreshData(_this)
  _this.activeName = 'tuison'
}

// 保存标签
zcrUtil.saveLabel = function (_this) {
  if (_this.tagOriData.length > 0) {
    axios.post(_this.ajaxpath + '/textLabel/save', {labels: _this.tagOriData})
        .then(function (response) {
          console.log('/textLabel/save:' + JSON.parse(response));
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
  }
}

// 保存要素
zcrUtil.saveExtract = function (_this) {
  if (_this.extractData.id == null) {
    return
  }
  axios.post(_this.ajaxpath + '/textExtract/save', {info: _this.extractData})
      .then(function (response) {
        console.log('/textExtract/save:' + JSON.parse(response.data));
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 保存错误排查
zcrUtil.saveExamine = function (_this) {
  if (_this.examineSaveData.id == null) {
    return
  }
  _this.examineSaveData.colors = _this.colorData
  _this.examineSaveData.contents = _this.warnData
  _this.examineSaveData.examines = _this.examineData
  axios.post(_this.ajaxpath + '/textExamine/save', {examine: _this.examineSaveData})
      .then(function (response) {
        console.log('/textExamine/save:' + JSON.parse(response));
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 保存意见
zcrUtil.saveComment = function (_this) {
  if (_this.commentsData.length > 0) {
    for (let i = 0; i < _this.commentsData.length; i++) {
      if (_this.commentsData[i].whetherAccept == false && !_this.commentsData[i].reason) {
        _this.$message({
          message: '请填写不接受原因！',
          type: 'warning'
        })
        return
      }
    }
    axios.post(_this.ajaxpath + '/textComment/save', {comments: _this.commentsData})
        .then(function (response) {
          console.log('/textComment/save:' + JSON.parse(response));
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
  }
  _this.$message({
    message: '保存成功！',
    type: 'success'
  })
}

// 添加批注收藏
zcrUtil.collectComment = function (_this, index) {
  let collects = {
    id: _this.createUUID(),
    textType: '审查意见',
    content: _this.commentsData[index].commentContent,
    contentId: _this.commentsData[index].commentId,
    textComment: _this.commentsData[index].comments,
    textLabel: _this.commentsData[index].commentLabel,
    textTime: _this.commentsData[index].commentTime,
    textPsnName: _this.commentsData[index].userName,
    textPsnId: _this.commentsData[index].userId,
    parentId: _this.contractId,
    parentName: _this.contractName,
    originId: _this.commentsData[index].id,
    knowledged: false,
    createTime: new Date(),
    createPsnId: _this.cUserId,
    createPsnName: _this.cUserName
  }
  axios.post(_this.ajaxpath + '/textCollect/save', {collects: collects})
      .then(function (response) {
        _this.$message({
          message: '收藏成功！',
          type: 'success'
        })
        _this.$set(_this.hasCollectData, index, true)
        console.log("aaaaaaaaa" + _this.hasCollectData)
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 添加批注收藏
zcrUtil.collectYComment = function (_this, index) {
  let collects = {
    id: _this.createUUID(),
    textType: '审查意见',
    content: _this.commentsYData[index].commentContent,
    contentId: _this.commentsYData[index].commentId,
    textComment: _this.commentsYData[index].comments,
    textLabel: _this.commentsYData[index].commentLabel,
    textTime: _this.commentsYData[index].commentTime,
    textPsnName: _this.commentsYData[index].userName,
    textPsnId: _this.commentsYData[index].userId,
    parentName: _this.contractName,
    parentId: _this.contractId,
    originId: _this.commentsYData[index].id,
    knowledged: false,
    createTime: new Date(),
    createPsnId: _this.cUserId,
    createPsnName: _this.cUserName
  }

  axios.post(_this.ajaxpath + '/textCollect/save', {collects: collects})
      .then(function (response) {
        _this.$message({
          message: '收藏成功！',
          type: 'success'
        })
        _this.$set(_this.hasCollectYData, index, true)
        console.log("aaaaaaaaa" + _this.hasCollectYData)
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 删除批注、标签收藏
zcrUtil.deleteCollectByOID = function (_this, id, index) {
  axios.post(_this.ajaxpath + '/textCollect/deleteByOID', {id: id})
      .then(function (response) {
        _this.$message({
          message: '已取消收藏！',
          type: 'success'
        })
        if (_this.leftSwitch) {
          _this.$set(_this.hasTagCollectData, index, false)
        } else {
          if (_this.activeSCJ == 'shoucang_bc') {
            _this.$set(_this.hasCollectData, index, false)
          } else {
            _this.$set(_this.hasCollectYData, index, false)
          }
        }
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}
// 删除标签收藏
zcrUtil.deleteCollectByOIDs = function (_this, id, index) {
  axios.post(_this.ajaxpath + '/textCollect/deleteByOID', {id: id})
      .then(function (response) {
        _this.$message({
          message: '已取消收藏！',
          type: 'success'
        })
        _this.$set(_this.hasTagCollectData, index, false)
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}
// 删除收藏
zcrUtil.deleteCollectById = function (_this, id) {
  axios.post(_this.ajaxpath + '/textCollect/delete', {id: id})
      .then(function (response) {
        _this.$message({
          message: '已取消收藏！',
          type: 'success'
        })
        _this.handleSCJlick()
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 添加标签收藏
zcrUtil.collectTag = function (_this, index) {

  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("getParagraphInfo", function (info) {
    let da = info.content.content
    let content
    for (let i = 0; i < da.length; i++) {
      if (da[i].paraId == _this.tagData[index].contentId) {
        content = da[i].content
      }
    }
    let collects = {
      id: _this.createUUID(),
      textType: '合同条款',
      content: content,
      contentId: _this.tagData[index].contentId,
      textComment: null,
      textLabel: _this.tagData[index].labelName,
      textLabelId: _this.tagData[index].labelId,
      textTime: _this.tagData[index].createTime,
      textPsnName: _this.tagData[index].createPsnName,
      textPsnId: _this.tagData[index].createPsnId,
      parentName: _this.contractName,
      parentId: _this.contractId,
      originId: _this.tagData[index].id,
      knowledged: false,
      createTime: new Date(),
      createPsnId: _this.cUserId,
      createPsnName: _this.cUserName
    }
    axios.post(_this.ajaxpath + '/textCollect/save', {collects: collects})
        .then(function (response) {
          _this.$message({
            message: '收藏成功！',
            type: 'success'
          })
          _this.$set(_this.hasTagCollectData, index, true)
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
  })
}
// 添加标签收藏
zcrUtil.collectTag2 = function (_this, index, coData) {

  const bsapi = window.getFrameAPI(_this.docId)
  bsapi.CallFrameAPI("getParagraphInfo", function (info) {
    let da = info.content.content
    let content
    for (let i = 0; i < da.length; i++) {
      if (da[i].paraId == coData.contentId) {
        content = da[i].content
      }
    }
    let collects = {
      id: _this.createUUID(),
      textType: '合同条款',
      content: content,
      contentId: coData.contentId,
      textComment: null,
      textLabel: coData.labelName,
      textLabelId: coData.labelId,
      textTime: coData.createTime,
      textPsnName: coData.createPsnName,
      textPsnId: coData.createPsnId,
      parentName: _this.contractName,
      parentId: _this.contractId,
      originId: coData.id,
      knowledged: false,
      createTime: new Date(),
      createPsnId: _this.cUserId,
      createPsnName: _this.cUserName
    }
    axios.post(_this.ajaxpath + '/textCollect/save', {collects: collects})
        .then(function (response) {
          _this.$message({
            message: '收藏成功！',
            type: 'success'
          })
          _this.$set(_this.hasTagCollectData, index, true)
        }).catch(function (error) { // 请求失败处理
      console.log(error);
    })
  })
}

// 刷新推送数据
zcrUtil.refreshData = function (_this) {
  _this.loading = true
  if (!_this.temp.item) {
    _this.pushData = []
    _this.temp = {
      item: null,
      page: 1,
      limit: 10,
      total: 0
    }
    _this.loading = false
    return
  }
  axios.post(_this.ajaxpath2 + '/external/tagSearch', _this.temp)
      .then(function (response) {
        _this.pushData = []
        let record = response.data.data.records
        for (let i = 0; i < record.length; i++) {
          let namech = ''
          if (record[i].kntypenamech) {
            namech = record[i].kntypenamech
          }
          let tData = {
            kntypenamech: namech,
            kname: '《' + record[i].kname,
            content: record[i].content,
            knid: record[i].knid,
            kntype: record[i].kntype
          }
          _this.pushData.push(tData)
        }
        _this.temp.total = response.data.data.total
        _this.loading = false
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 刷新推送数据
zcrUtil.refreshData2 = function (_this) {
  axios.post(_this.ajaxpath2 + '/external/tagSearch', _this.temp)
      .then(function (response) {
        _this.pushData = []
        let record = response.data.data.records
        for (let i = 0; i < record.length; i++) {
          let namech = ''
          if (record[i].kntypenamech) {
            namech = record[i].kntypenamech
          }
          let tData = {
            kntypenamech: namech,
            kname: '《' + record[i].kname,
            content: record[i].content,
            knid: record[i].knid,
            kntype: record[i].kntype
          }
          _this.pushData.push(tData)
        }
        _this.temp.total = response.data.data.total
        _this.activeName = 'tuison'
      }).catch(function (error) { // 请求失败处理
    console.log(error);
  })
}

// 获取字符在字符串中位置
zcrUtil.getStrPositions = function (str, subStr) {
  var indexs = [];
  var string = str;
  while (true) {
    var index = string.lastIndexOf(subStr);
    if (index != -1) {
      string = string.substr(0, index) + string.substr(index + subStr.length, string.length);
      indexs.push(index);
    } else {
      break;
    }
  }
  return indexs;
}

Date.prototype.Format = function (fmt) {

//author:wangweizhen

  var o = {

    "M+": this.getMonth() + 1,                 //月份

    "d+": this.getDate(),                    //日

    "h+": this.getHours(),                   //小时

    "m+": this.getMinutes(),                 //分

    "s+": this.getSeconds(),                 //秒

    "q+": Math.floor((this.getMonth() + 3) / 3), //季度

    "S": this.getMilliseconds()             //毫秒

  };

  if (/(y+)/.test(fmt))

    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));

  for (var k in o)

    if (new RegExp("(" + k + ")").test(fmt))

      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));

  return fmt;

}
