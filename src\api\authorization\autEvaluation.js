import { request } from '@/api/index';

export default {
    query(data) {
        return request({
            url: '/autEvaluation/getEvaluationList',
            method: 'post',
            data,
        });
    },
    queryDialog(data) {
        return request({
            url: '/autEvaluation/getDialogList',
            method: 'post',
            data,
        });
    },
    refresh() {
        return request({
            url: '/autEvaluation/refresh',
            method: 'post',
        });
    }
};
