import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawyerB<PERSON>ckList/getBlackList',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawyerB<PERSON>ckList/save',
            method: 'post',
            data

        })
    },
    queryDataById(data) {
        return request({
            url: '/lawyerBlackList/getDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/lawyerBlackList/delete',
            method: 'post',
            data
        })
    },
    distinct(data) {
        return request({
            url: '/lawyerBlackList/distinct',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/lawyerBlackList/setParam',
            method: 'post',
            data
        })
    }
}
