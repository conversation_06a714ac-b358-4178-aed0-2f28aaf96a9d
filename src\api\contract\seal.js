import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/seal/query',
            method: 'post',
            data
        })
    },
  queryOwnData(data) {
        return request({
            url: '/seal/queryOwnData',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/seal/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/seal/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/seal/queryById',
            method: 'post',
            data
        })
    },
  checkOnly(data) {
    return request({
      url: '/seal/checkOnly',
      method: 'post',
      data
    })
  },
  stopSave(data) {
    return request({
      url: '/seal/stopSave',
      method: 'post',
      data
    })
  }
}