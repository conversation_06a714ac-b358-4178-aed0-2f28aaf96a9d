import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/mainUserConfig/query',
            method: 'post',
            data
        })
    },
    queryOgnList(data) {
        return request({
            url: '/mainUserConfig/queryOgnList',
            method: 'post',
            data
        })
    },
    queryPart(data) {
        return request({
            url: '/userConfig/queryPart',
            method: 'post',
            data
        })
    },
    addUser(data) {
        return request({
            url: '/mainUserConfig/addUser',
            method: 'post',
            data
        })
    },
    addUserPart(data) {
        return request({
            url: '/userConfig/addUserPart',
            method: 'post',
            data
        })
    },
    getMainDataByStaffCode(data) {
        return request({
            url: '/mainUserConfig/getMainDataByStaffCode',
            method: 'post',
            data
        })
    },
    queryProcessUserData(data){
        return request({
            url: '/mainUserConfig/queryProcessUserData',
            method: 'post',
            data
        })
    }
}
