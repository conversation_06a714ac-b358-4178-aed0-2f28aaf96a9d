import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/bm-CaseRisk/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/bm-CaseRisk/save',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/bm-CaseRisk/query_by_id',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    deleteById(data) {
        return request({
            url: '/bm-CaseRisk/deleteById',
            method: 'post',
            data

        })
    },
    setParam(data) {
        return request({
            url: '/bm-CaseRisk/setParam',
            method: 'post',
            data
        })
    },
    queryDialog(data) {
        return request({
            url: '/bm-CaseRisk/queryDialog',
            method: 'post',
            data
        })
    },
    exportRiskLedger(data) {
        return request({
            url: '/bm-CaseRisk/exportRiskLedger',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    createPdf(data) {
        return request({
            url: '/bm-CaseRisk/createPdf',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
}

