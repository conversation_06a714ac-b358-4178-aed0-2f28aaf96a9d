import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/supervise/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/supervise/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/supervise/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/supervise/queryById',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/caseExamineManage/setParam',
            method: 'post',
            data
        })
    }
}
