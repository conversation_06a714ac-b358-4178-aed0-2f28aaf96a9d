import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/contractapproval/queryConditionList',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/contractapproval/saveConditionList',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/contractapproval/queryConditionListById',
            method: 'post',
            data
        })
    },
    stopSave(data) {
        return request({
            url: '/contractapproval/stopConditionSave',
            method: 'post',
            data
        })
    }
}