import axios from "@/js_public/axios";

// 保存公告
export const saveAnnouncement = data => {
  return axios.post("/sgAnnouncement/save", data);
}

// 批量保存，例如批量发布使用
export const saveAnnouncementList = data => {
  return axios.post("/sgAnnouncement/saves", data);
}

// 获取公告列表
export const getAnnouncementList = data => {
  return axios.post("/sgAnnouncement/list", data);
}

// 批量删除公告
export const deleteAnnouncement = data => {
  return axios.post("/sgAnnouncement/deletes", data);
}

// 根据用户编码获取公告
export const getAnnouncementByEmpCode = data => {
  return axios.post("/sgAnnouncement/listByEmp", data);
}

// 更新系统公告接收人读取状态
export const updateAnnouncementReadStatus = data => {
  return axios.post("/sgAnnouncement/saveReceiver", data);
}
