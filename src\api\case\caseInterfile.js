import {request} from '@/api/index'

export default {

  initCaseInterfile(data) {
    return request({
      timeout: 20000,
      url: '/CaseInterfile/initCaseInterfile',
      method: 'post',
      data
    })
  },

  save(data) {
    return request({
      url: '/CaseInterfile/save',
      method: 'post',
      data
    })
  },

  queryByCaseId(data) {
    return request({
      url: '/CaseInterfile/queryByCaseId',
      method: 'post',
      data: {
        id: data
      }
    })
  }

}
