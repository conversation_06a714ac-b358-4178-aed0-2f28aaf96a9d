import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/user-custom-menu/query',
            method: 'post',
            data
        })
    },
    queryHome(data) {
        return request({
            url: '/user-custom-menu/queryHome',
            method: 'post',
            data
        })
    },
    queryList(data) {
        return request({
            url: '/user-custom-menu/queryList',
            method: 'post'
        })
    },
    save(data) {
        return request({
            url: '/user-custom-menu/save',
            method: 'post',
            data
        })
    },
    stop(data) {
        return request({
            url: '/user-custom-menu/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/user-custom-menu/delete',
            method: 'post',
            data
        })
    },
}
