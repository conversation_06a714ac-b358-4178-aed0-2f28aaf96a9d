import {request} from '@/api/index'

export default {

  query(data) {
    return request({
      url: '/cooperation/query',
      method: 'post',
      data
    })
  },

  save(data) {
    return request({
      url: '/cooperation/save',
      method: 'post',
      data
    })
  },

  createTransferBean(data) {
    return request({
      url: '/cooperation/createTransferBean',
      method: 'post',
      data
    })
  },

  queryById(data) {
    return request({
      url: '/cooperation/queryById',
      method: 'post',
      data: {
        id: data
      }
    })
  },
  deleteData(data) {
    return request({
      url: '/cooperation',
      method: 'delete',
      data
    })
  },
  saveReply(data) {
    return request({
      url: '/cooperation/saveReply',
      method: 'post',
      data
    })
  },

  queryByProcessId(data) {
    return request({
      url: '/cooperation/queryByProcessId',
      method: 'post',
      data
    })
  },
  queryByDisputeId(data) {
    return request({
      url: '/cooperation/queryByDisputeId',
      method: 'post',
      data
    })
  },
  queryByCaseId(data) {
    return request({
      url: '/cooperation/queryByCaseId',
      method: 'post',
      data
    })
  }
}
