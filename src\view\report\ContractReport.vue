<template>
  <div class="contract-report">
    <el-card class="box-card">

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="所属板块">
            <el-select
                v-model="filterForm.belongPlate"
                placeholder="请选择所属板块"
                clearable
                @change="handleBelongPlateChange">
              <el-option
                  v-for="item in belongPlateList"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="经办单位">
            <el-select
                v-model="filterForm.handlingUnit"
                placeholder="请选择经办单位"
                clearable
                :disabled="!filterForm.belongPlate">
              <el-option
                  v-for="item in handlingUnitList"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年度">
            <el-date-picker
                v-model="filterForm.year"
                type="year"
                placeholder="选择年度"
                format="yyyy"
                value-format="yyyy">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="月度">
            <el-select v-model="filterForm.month" placeholder="请选择月度" clearable>
              <el-option
                  v-for="month in 12"
                  :key="month"
                  :label="`${month}月`"
                  :value="month">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计表格 -->
      <div class="table-section">
              <div slot="header" class="clearfix">
                <el-button style="float: right; padding: 3px 0" type="text" @click="exportReport">
                  <i class="el-icon-download"></i> 导出报表
                </el-button>
              </div>
        <el-table
            :data="pagedReportData"
            v-loading="loading"
            style="width: 100%; flex: 1;"
            border
            height="400"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            :row-key="(row, index) => index"
            @row-click="handleRowClick">

          <!-- 序号列 -->
          <el-table-column prop="serialNumber" label="序号" width="30" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>

          <!-- 所属板块列 -->
          <el-table-column prop="belongPlate" label="所属板块" width="130" align="center">
          </el-table-column>

          <!-- 经办单位列 -->
          <el-table-column prop="handlingUnit" label="经办单位" width="300" align="center">
          </el-table-column>

          <!-- 全部系统统计 -->
          <el-table-column label="合同履行计划填报统计(全部)" align="center">
            <el-table-column prop="allContractCount" label="合同数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell">{{ scope.row.allContractCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="allReportedCount" label="填报数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell success">{{ scope.row.allReportedCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="allUnreportedCount" label="未填报数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell warning">{{ scope.row.allUnreportedCount }}</span>
              </template>
            </el-table-column>
          </el-table-column>

          <!-- 法务系统统计 -->
          <el-table-column label="合同履行计划填报统计(法务系统)" align="center">
            <el-table-column prop="legalContractCount" label="合同数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell">{{ scope.row.legalContractCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="legalReportedCount" label="填报数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell success">{{ scope.row.legalReportedCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="legalUnreportedCount" label="未填报数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell warning">{{ scope.row.legalUnreportedCount }}</span>
              </template>
            </el-table-column>
          </el-table-column>

          <!-- 业务系统统计 -->
          <el-table-column label="合同履行计划填报统计(业务系统)" align="center">
            <el-table-column prop="businessContractCount" label="合同数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell">{{ scope.row.businessContractCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="businessReportedCount" label="填报数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell success">{{ scope.row.businessReportedCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="businessUnreportedCount" label="未填报数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="number-cell warning">{{ scope.row.businessUnreportedCount }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import {
  queryContractPerformReport,
  exportContractPerformReport,
  queryBelongPlateList,
  queryHandlingUnitList
} from '@/api/report/contractReport'

export default {
  name: 'ContractReport',
  data() {
    return {
      loading: false,
      filterForm: {
        belongPlate: '',
        handlingUnit: '',
        year: '',
        month: ''
      },
      belongPlateList: [],
      handlingUnitList: [],
      reportData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    pagedReportData() {
      const { currentPage, pageSize } = this.pagination
      const start = (currentPage - 1) * pageSize
      const end = start + pageSize
      return Array.isArray(this.reportData) ? this.reportData.slice(start, end) : []
    }
  },
  created() {
    console.log('组件创建时 belongPlateList:', this.belongPlateList)
  },
  mounted() {
    console.log('组件挂载时 belongPlateList:', this.belongPlateList)
    this.getBelongPlateList()
    this.getReportData()
  },
  methods: {
    // 获取所属板块列表
    async getBelongPlateList() {
      try {
        const response = await queryBelongPlateList()
        console.log('所属板块API返回:', response)
        this.belongPlateList = response.data.data || []
      } catch (error) {
        console.error('获取所属板块列表失败', error)
        this.$message.error('获取所属板块列表失败')
      }
    },

    // 所属板块变化处理
    async handleBelongPlateChange(belongPlate) {
      this.filterForm.handlingUnit = ''
      this.handlingUnitList = []

      if (belongPlate) {
        try {
          const response = await queryHandlingUnitList(belongPlate)
          console.log('经办单位API返回:', response)
          this.handlingUnitList = response.data.data || []
        } catch (error) {
          console.error('获取经办单位列表失败', error)
          this.$message.error('获取经办单位列表失败')
        }
      }
    },

    // 获取报表数据
    async getReportData() {
      this.loading = true
      try {
        const params = {
          ...this.filterForm
        }
        const response = await queryContractPerformReport(params)
        console.log('API返回数据:', response)

        // 根据返回的数据结构，数据在 response.data.data 中
        if (response && response.data && response.data.data) {
          this.reportData = response.data.data
          this.pagination.total = this.reportData.length
          console.log('设置到表格的数据:', this.reportData)
          console.log('数据长度:', this.reportData.length)



          // 强制更新视图
          this.$nextTick(() => {
            console.log('视图更新后的数据:', this.reportData)
          })
        } else {
          console.error('返回数据格式不正确:', response)
          this.reportData = []
          this.pagination.total = 0
        }
      } catch (error) {
        console.error('获取报表数据失败', error)
        this.$message.error('获取报表数据失败')
        this.reportData = []
        this.pagination.total = 0
      } finally {
        this.loading = false
      }
    },

    // 查询
    handleSearch() {
      this.pagination.currentPage = 1
      this.getReportData()
    },

    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        belongPlate: '',
        handlingUnit: '',
        year: '',
        month: ''
      }
      this.handlingUnitList = []
      this.handleSearch()
    },

    // 导出报表
    async exportReport() {
      try {
        const params = {
          ...this.filterForm
        }
        const response = await exportContractPerformReport(params)

        // 创建下载链接
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `合同履行计划填报统计报表_${new Date().toLocaleDateString()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)

        this.$message.success('报表导出成功')
      } catch (error) {
        console.error('导出报表失败', error)
        this.$message.error('导出报表失败')
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val
      // 不需要重新请求数据，只需要更新分页
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val
      // 不需要重新请求数据，只需要更新分页
    },

    // 行点击事件（调试用）
    handleRowClick(row, column, event) {
      console.log('点击行数据:', row)
      console.log('点击列:', column)
    },

    // 测试方法 - 添加测试数据
    addTestData() {
      this.reportData = [
        {
          belongPlate: "测试板块",
          handlingUnit: "测试单位",
          allContractCount: 10,
          allReportedCount: 5,
          allUnreportedCount: 5,
          legalContractCount: 6,
          legalReportedCount: 3,
          legalUnreportedCount: 3,
          businessContractCount: 4,
          businessReportedCount: 2,
          businessUnreportedCount: 2
        }
      ]
      this.pagination.total = this.reportData.length
      console.log('添加测试数据:', this.reportData)
    }
  }
}
</script>

<style scoped>
.contract-report {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.box-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.box-card .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filter-section {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.filter-title {
  color: #f56c6c;
  font-weight: bold;
  margin-bottom: 15px;
  font-size: 14px;
}

.filter-form {
  margin-bottom: 0;
}

.table-section {
  /*margin-top: 20px;*/
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.number-cell {
  font-weight: bold;
}

.number-cell.success {
  color: #67c23a;
}

.number-cell.warning {
  color: #e6a23c;
}

/* 表格头部样式 */
.el-table th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-weight: bold;
}

/* 表格行样式 */
.el-table td {
  padding: 8px 0;
}

/* 筛选条件标签样式 */
.el-form-item__label {
  color: #f56c6c;
  font-weight: bold;
}

/* 修复表格滚动被主题样式隐藏的问题 */
::v-deep .contract-report .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto !important;
}
::v-deep .contract-report .el-table__body-wrapper {
  overflow-x: hidden;
}
</style>
