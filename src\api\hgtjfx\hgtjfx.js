import { request } from '@/api/index';

export default {
	querydata(data) {
		return request({
			url: '/complianceRiskIndex/getBusinessDomainData',
			method: 'post',
			data,
		});
	},
	querydata1(data) {
		return request({
			url: '/complianceManageIndex/getEnabledCompliancePersonCount',
			method: 'post',
			data,
		});
	},
	querydata2(data) {
		return request({
			url: '/complianceManageIndex/getComplianceReviewCount',
			method: 'post',
			data,
		});
	},
	querydata3(data) {
		return request({
			url: '/complianceManageIndex/getNeedRectifyComplianceReviewCount',
			method: 'post',
			data,
		});
	},
	querydata4(data) {
		return request({
			url: '/complianceManageIndex/getEnabledCompliancePersonCountByOrg',
			method: 'post',
			data,
		});
	},
	querydata5(data) {
		return request({
			url: '/complianceManageIndex/getSubmittedComplianceReviewCountByCategory',
			method: 'post',
			data,
		});
	},
	querydata6(data) {
		return request({
			url: '/complianceManageIndex/getComplianceRectificationFeedbackCount',
			method: 'post',
			data,
		});
	},
	querydata7(data) {
		return request({
			url: '/complianceManageIndex/getSubmittedComplianceCaseCount',
			method: 'post',
			data,
		});
	},
	querydata8(data) {
		return request({
			url: '/complianceManageIndex/getSubmittedPolicyCount',
			method: 'post',
			data,
		});
	},
	querydata9(data) {
		return request({
			url: '/complianceManageIndex/getComplianceRiskIdentificationCount',
			method: 'post',
			data,
		});
	},
	querydata10(data) {
		return request({
			url: '/complianceManageIndex/getJobDutyCount',
			method: 'post',
			data,
		});
	},
	querydata11(data) {
		return request({
			url: '/complianceManageIndex/getProcessControlCount',
			method: 'post',
			data,
		});
	},
	querydata12(data) {
		return request({
			url: '/complianceManageIndex/getComplianceSystemCount',
			method: 'post',
			data,
		});
	},
	querydata13(data) {
		return request({
			url: '/complianceManageIndex/getComplianceSystemStatusCount',
			method: 'post',
			data,
		});
	},
};
