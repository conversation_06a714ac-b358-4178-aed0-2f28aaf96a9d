import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/LawFirmEvaluate/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/LawFirmEvaluate/save',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/LawFirmEvaluate/queryDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/LawFirmEvaluate/delete',
            method: 'post',
            data
        })
    },
    getDetailList(data) {
        return request({
            url: '/LawFirmEvaluate/getDetailList',
            method: 'post',
            data
        })
    },
    dafen(data) {
        return request({
            url: '/LawFirmEvaluate/dafen',
            method: 'post',
            data
        })
    },
    queryData(data) {
        return request({
            url: '/LawFirmEvaluate/queryData',
            method: 'post',
            data
        })
    },
}
