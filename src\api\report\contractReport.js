import request from '@/js_public/axios'

// 查询合同履行计划填报统计报表
export function queryContractPerformReport(data) {
  return request({
    url: '/contractPerformReport/query',
    method: 'post',
    data: JSON.stringify(data)
  })
}

// 导出合同履行计划填报统计报表
export function exportContractPerformReport(data) {
  return request({
    url: '/contractPerformReport/export',
    method: 'post',
    data: JSON.stringify(data),
    responseType: 'blob'
  })
}

// 查询所属板块列表
export function queryBelongPlateList() {
  return request({
    url: '/contractPerformReport/belongPlateList',
    method: 'get'
  })
}

// 查询经办单位列表
export function queryHandlingUnitList(belongPlate) {
  return request({
    url: '/contractPerformReport/handlingUnitList',
    method: 'get',
    params: { belongPlate }
  })
}
