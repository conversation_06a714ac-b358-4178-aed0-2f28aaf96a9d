import {request} from '@/api'

export default {


    querySeachData(data) {
        return request({
            url: '/General/query_seach_data',
            method: 'post',
            data
        })
    },

    query(data) {
        return request({
            url: '/General/query',
            method: 'post',
            data
        })
    },

    queryTable(data) {
        return request({
            url: '/General/queryTabless',
            method: 'post',
            data
        })
    },

    queryStatus(data) {
        return request({
            url: '/General/queryStatus',
            method: 'post',
            data
        })
    },
    addText(data) {
        return request({
            url: '/General/addText',
            method: 'post',
            data
        })
    },
    updateText(data) {
        return request({
            url: '/General/updateText',
            method: 'post',
            data
        })
    },

    distinct(data) {
        return request({
            url: '/General/distinct',
            method: 'post',
            data
        })
    },

}
