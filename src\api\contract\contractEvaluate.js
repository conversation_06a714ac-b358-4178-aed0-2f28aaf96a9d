import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/contractEvaluateMain/query',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/contractEvaluateMain/queryDataById',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/contractEvaluateMain/save',
            method: 'post',
            data
        })
    },
    saveByPerformComplete(data) {
        return request({
            url: '/contractEvaluateMain/saveByPerformComplete',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/contractEvaluateMain/delete',
            method: 'post',
            data
        })
    },
    getDetailList(data) {
        return request({
            url: '/contractEvaluateMain/getDetailList',
            method: 'post',
            data
        })
    },
    grade(data) {
        return request({
            url: '/contractEvaluateMain/grade',
            method: 'post',
            data
        })
    },
    queryData(data) {
        return request({
            url: '/contractEvaluateMain/queryData',
            method: 'post',
            data
        })
    },
    queryEvaluateDialog(data){
        return request({
            url: '/contractEvaluateMain/queryData',
            method: 'post',
            data
        })
    }
}
