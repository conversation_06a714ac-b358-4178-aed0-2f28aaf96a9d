import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawFirmOutApprovalMain/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawFirmOutApprovalMain/save',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/lawFirmOutApprovalMain/queryDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/lawFirmOutApprovalMain/delete',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/lawFirmOutApprovalMain/setParam',
            method: 'post',
            data
        })
    }
}
