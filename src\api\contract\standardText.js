import {request} from '@/api/index'

export default {
  ADLogin(data) {
    return request({
      url: '/standardtext/ADLogin',
      method: 'post',
      data
    })
  },
  queryStandardAttList(data) {
    return request({
      url: '/standardtext/queryStandardAttList',
      method: 'post',
      data
    })
  },
  previewStandardAttById(data) {
    return request({
      url: '/standardtext/previewStandardAttById',
      method: 'post',
      data
    })
  },
  getIsCancel(data) {
    return request({
      url: '/standardtext/getIsCancel',
      method: 'post',
      data
    })
  },
  cancelContract(data) {
    return request({
      url: '/standardtext/cancelContract',
      method: 'post',
      data
    })
  },
  upload(data) {
    return request({
      url: '/standardtext/upload',
      method: 'post',
      data
    })
  },
  getContractAttachment(data) {
    return request({
      url: '/standardtext/getContractAttachment',
      method: 'post',
      data
    })
  },
  getFieldMapList(data) {
    return request({
      url: '/standardtext/getFieldMapList',
      method: 'post',
      data
    })
  },
  editStandardAttById(data) {
    return request({
      url: '/standardtext/editStandardAttById',
      method: 'post',
      data
    })
  },
  versionCheck(data) {
    return request({
      url: '/standardtext/versionCheck',
      method: 'post',
      data
    })
  },
}