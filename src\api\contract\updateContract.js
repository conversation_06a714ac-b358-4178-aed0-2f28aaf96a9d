import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/updateContract/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/updateContract/save',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/updateContract/queryById',
            method: 'post',
            data
        })
    },
    stopSave(data) {
        return request({
            url: '/updateContract/stopConditionSave',
            method: 'post',
            data
        })
    }
}