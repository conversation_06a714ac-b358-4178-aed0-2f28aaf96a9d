import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/dispute/query',
            method: 'post',
            data
        })
    },
    query2(data) {
        return request({
            url: '/dispute/query2',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/dispute/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/dispute/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/dispute/queryById',
            method: 'post',
            data
        })
    },
    queryLeft(data) {
        return request({
            url: '/dispute/queryLeft',
            method: 'post',
            data
        })
    },
    queryLeft2(data) {
        return request({
            url: '/dispute/queryLeft2',
            method: 'post',
            data
        })
    }

}
