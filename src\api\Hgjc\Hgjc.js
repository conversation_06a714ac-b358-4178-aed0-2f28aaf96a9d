import {request} from '@/api/index';
export default{
    query(data){
        return request({
            url:'/complianceChack/query',
            method:'post',
            data
        })
    },

    queryBank(data){
        return request({
            url:'/complianceChack/queryBank',
            method:'post',
            data
        })
    },

    save(data){
        return request({
            url:'/complianceChack/addChackTask',
            method:"post",
            data
        })
    },

    delete(data){
        return request({
            url:'/complianceChack/delete/' + data,
            method:'get',
        })
    },

    queryById(data){
        return request({
            url:'/complianceChack/queryById/' + data,
            method:'get' 
        })
    },
    
    sendMessageForCreater(data,pageName){
        return request(
            {
                url: '/complianceChack/sendMessageCreater/' + pageName,
                method:'post',
                data
            }
        )
    }
}