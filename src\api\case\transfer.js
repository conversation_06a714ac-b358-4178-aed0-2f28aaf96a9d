import {request} from '@/api/index'

export default {
  // 新增共享
  createTransferBean(data) {
    return request({
      url: '/Transfer/createTransferBean',
      method: 'post',
      data
    })
  },
  // 新增移交
  createTurnover(data) {
    return request({
      url: '/Transfer/createTurnover',
      method: 'post',
      data
    })
  },
  // 根据参数查询移交数据
  queryTransferBean(data) {
    return request({
      url: '/Transfer/queryTransferBean',
      method: 'post',
      data
    })
  },
  queryTransferBeanView(data) {
    return request({
      url: '/Transfer/queryTransferBeanView',
      method: 'post',
      data
    })
  },
  deleteId(data) {
    return request({
      url: '/Transfer',
      method: 'delete',
      data
    })
  },
  // 根据参数查询移交数据
  updateTransferState(data) {
    return request({
      url: '/Transfer/updateTransferState',
      method: 'post',
      data
    })
  },
  // 根据移交ID查询数据
  queryTransferById(data) {
    return request({
      url: '/Transfer/queryTransferById',
      method: 'post',
      data
    })
  },
  // 根据移交ID查询数据
  getMode(data) {
    return request({
      url: '/Transfer/getMode',
      method: 'post',
      data
    })
  }
}
