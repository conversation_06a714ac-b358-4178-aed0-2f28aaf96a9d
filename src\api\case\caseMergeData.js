import { request } from '@/api/index'
export default {
  // 查询所有案件的分类信息（最多含有两层分类信息）
  queryLargeClass() {
    return request({
      url: '/alphaCaseData/queryLargeClass',
      method: 'get'
    })
  },
  // 智能检索-生成检索条件
  intelligentSearch(data) {
    return request({
      url: '/alphaCaseData/intelligentSearch',
      method: 'post',
      data
    })
  },
  // 查询案例列表
  queryCaseList(data) {
    return request({
      url: '/alphaCaseData/queryCaseList',
      method: 'post',
      data
    })
  },
  // 查询案例可视化
  statisticsByFields(data) {
    return request({
      url: '/alphaCaseData/statisticsByFields',
      method: 'post',
      data
    })
  },
  // 保存类案分析报告
  saveReport(data) {
    return request({
      url: '/alphaCaseData/saveReport',
      method: 'post',
      data
    })
  },
  // 判断是否保存了类案分析报告
  selectReport(data) {
    return request({
      url: '/alphaCaseData/selectReport',
      method: 'post',
      data
    })
  },
  // 获取案例对应的法条信息
  jfindJudgmentByIdlaw(data) {
    return request({
      url: '/alphaCaseData/jfindJudgmentByIdLaw',
      method: 'post',
      data
    })
  },
  // 获取法条详细内容
  jfindJudgmentByIdlawContent(data) {
    return request({
      url: '/alphaCaseData/jfindJudgmentByIdlawContent',
      method: 'post',
      data
    })
  },
  // 批量下载案例
  jmultiDownload(data) {
    return request({
      url: '/alphaCaseData/jmultiDownload',
      method: 'post',
      data
    })
  },
  // 批量下载案例
  jgenerationList(data) {
    return request({
      url: '/alphaCaseData/jgenerationList',
      method: 'post',
      data
    })
  },
  // 根据lid下载法规
  lmultidownloadbyid(data) {
    return request({
      url: '/alphaCaseData/lmultidownloadbyid',
      method: 'post',
      data
    })
  },
  // 查询案例详情
  jfindJudgmentById(data) {
    return request({
      url: '/alphaCaseData/jfindJudgmentById',
      method: 'post',
      data
    })
  },
}
