<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>岗位职责详细说明测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .contract-form {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .node-selector {
            margin: 20px 0;
        }

        .responsibility-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
            color: #666;
            font-size: 13px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>岗位职责详细说明测试</h1>
            <p>测试各个审批节点的岗位职责详细说明显示</p>
        </div>

        <div class="demo-section">
            <h3>📋 选择审批节点</h3>
            <div class="node-selector">
                <el-select v-model="selectedNodeId" @change="onNodeChange" placeholder="选择审批节点" style="width: 100%;">
                    <el-option v-for="node in nodeList" :key="node.nodeId" :label="node.name" :value="node.nodeId">
                        <span style="float: left">{{ node.name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ node.nodeId }}</span>
                    </el-option>
                </el-select>
            </div>
            
            <div style="margin-top: 20px;">
                <el-button @click="showAllResponsibilities" type="primary">查看所有岗位职责</el-button>
                <el-button @click="testRandomNode" type="success">随机测试节点</el-button>
            </div>
        </div>

        <!-- 模拟合同审批单界面 -->
        <div class="contract-form">
            <!-- 岗位职责显示行 -->
            <el-row v-if="currentNodeInfo" style="margin-top: 10px;">
                <el-col :span="24" style="text-align: center;">
                    <el-tag type="info" size="medium" style="padding: 8px 16px; font-size: 14px;">
                        <i class="el-icon-user-solid" style="margin-right: 5px;"></i>
                        当前审批节点：{{ currentNodeInfo }}
                    </el-tag>
                </el-col>
            </el-row>
            
            <!-- 岗位职责详细说明 -->
            <el-row v-if="currentNodeResponsibility" style="margin-top: 10px;">
                <el-col :span="24">
                    <el-card shadow="never" style="background: #f8f9fa; border: 1px solid #e9ecef;">
                        <div style="text-align: center; color: #666; font-size: 13px; line-height: 1.6;">
                            <i class="el-icon-document" style="margin-right: 5px;"></i>
                            <strong>岗位职责：</strong>{{ currentNodeResponsibility }}
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            
            <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">新增合同审批单</span>
            </el-row>

            <!-- 模拟表单内容 -->
            <div style="margin-top: 20px; padding: 20px; border: 1px solid #e9ecef; border-radius: 4px;">
                <p><strong>合同名称：</strong>测试合同岗位职责显示</p>
                <p><strong>合同编号：</strong>TEST2025001</p>
                <p><strong>合同类型：</strong>技术服务合同</p>
                <p><strong>合同金额：</strong>100,000.00元</p>
                <p><strong>当前状态：</strong>审批中</p>
            </div>
        </div>

        <!-- 所有岗位职责展示 -->
        <div class="demo-section" v-if="showAllNodes">
            <h3>📋 所有岗位职责一览</h3>
            <div v-for="node in nodeList" :key="node.nodeId" style="margin-bottom: 15px;">
                <el-card shadow="hover">
                    <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                        <span><strong>{{ node.name }}</strong></span>
                        <el-tag size="mini" type="info">{{ node.nodeId }}</el-tag>
                    </div>
                    <div style="color: #666; font-size: 13px; line-height: 1.6;">
                        {{ node.responsibility }}
                    </div>
                </el-card>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                selectedNodeId: 'Activity_1rmp80v',
                currentNodeInfo: null,
                currentNodeResponsibility: null,
                showAllNodes: false,
                // 所有节点信息
                nodeList: [
                    {
                        nodeId: 'Activity_1rmp80v',
                        name: '业务部门负责人',
                        responsibility: '负责审核合同的业务合理性、技术可行性，确认合同内容符合业务部门需求，评估合同执行的资源配置和风险控制措施。'
                    },
                    {
                        nodeId: 'Activity_1yc9eu3',
                        name: '运改部审核',
                        responsibility: '负责审核合同中涉及的运营改革事项，确保合同条款符合公司运营管理要求，评估对现有运营流程的影响。'
                    },
                    {
                        nodeId: 'Activity_1upb5zy',
                        name: '税务审核',
                        responsibility: '负责审核合同的税务合规性，确认税率适用正确，税务处理符合相关法规，评估税务风险和优化建议。'
                    },
                    {
                        nodeId: 'Activity_0hgi73c',
                        name: '资金审核',
                        responsibility: '负责审核合同的资金安排和支付条款，确认资金来源充足，支付计划合理，评估资金风险和现金流影响。'
                    },
                    {
                        nodeId: 'Activity_0no6qkt',
                        name: '财务部部长',
                        responsibility: '负责从财务角度全面审核合同，包括成本效益分析、预算匹配、财务风险评估，确保合同符合财务管理制度。'
                    },
                    {
                        nodeId: 'Activity_1qs8r6i',
                        name: '法务部风控',
                        responsibility: '负责识别和评估合同中的法律风险，审核合同条款的合规性，提出风险防控措施和法律建议。'
                    },
                    {
                        nodeId: 'Activity_1lee3nx',
                        name: '办公室',
                        responsibility: '负责审核合同的行政管理事项，包括印章使用、档案管理、流程规范性，确保合同管理符合公司制度。'
                    },
                    {
                        nodeId: 'Activity_1umzmjb',
                        name: '公司领导',
                        responsibility: '负责从公司战略高度审核合同，评估合同对公司整体发展的影响，做出最终审批决定。'
                    },
                    {
                        nodeId: 'Activity_0wn3tir',
                        name: '返回经办人',
                        responsibility: '负责根据审批意见对合同进行修改完善，处理审批过程中发现的问题，重新提交审批。'
                    },
                    {
                        nodeId: 'Activity_0y3xjh6',
                        name: '法务承办人',
                        responsibility: '负责合同的法务审核工作，包括条款合规性检查、法律风险识别、合同文本规范性审核。'
                    },
                    {
                        nodeId: 'Activity_1e2ebp6',
                        name: '合同专业负责人',
                        responsibility: '负责从专业角度审核合同技术条款、质量标准、履行要求，确保合同专业内容准确可行。'
                    },
                    {
                        nodeId: 'Activity_0pdswu8',
                        name: '法务部部长',
                        responsibility: '负责法务部门的统筹管理，对重要合同进行最终法务审核，确保法律风险得到有效控制。'
                    },
                    {
                        nodeId: 'Activity_0pz4x4e',
                        name: '首席合规官',
                        responsibility: '负责从合规管理角度审核合同，确保合同符合国家法律法规和公司合规要求，防范合规风险。'
                    },
                    {
                        nodeId: 'Activity_1r1du0j',
                        name: '三级审批',
                        responsibility: '负责按照三级审批制度对合同进行审核，根据合同金额和重要程度确定审批权限和流程。'
                    },
                    {
                        nodeId: 'Activity_0u0241c',
                        name: '三级审批',
                        responsibility: '负责按照三级审批制度对合同进行审核，根据合同金额和重要程度确定审批权限和流程。'
                    }
                ]
            },
            mounted() {
                // 默认选择第一个节点
                this.onNodeChange(this.selectedNodeId);
            },
            methods: {
                // 节点选择变化
                onNodeChange(nodeId) {
                    const selectedNode = this.nodeList.find(node => node.nodeId === nodeId);
                    if (selectedNode) {
                        this.currentNodeInfo = selectedNode.name;
                        this.currentNodeResponsibility = selectedNode.responsibility;
                        this.$message.success(`已选择节点: ${selectedNode.name}`);
                    }
                },
                
                // 显示所有岗位职责
                showAllResponsibilities() {
                    this.showAllNodes = !this.showAllNodes;
                    if (this.showAllNodes) {
                        this.$message.info('显示所有岗位职责');
                    } else {
                        this.$message.info('隐藏岗位职责列表');
                    }
                },
                
                // 随机测试节点
                testRandomNode() {
                    const randomIndex = Math.floor(Math.random() * this.nodeList.length);
                    const randomNode = this.nodeList[randomIndex];
                    this.selectedNodeId = randomNode.nodeId;
                    this.onNodeChange(randomNode.nodeId);
                }
            }
        });
    </script>
</body>
</html>
