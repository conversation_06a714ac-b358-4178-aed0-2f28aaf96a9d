import {request} from '@/api/index';

export default{

    query(data){
        return request({
            url:'/ComplianceTrainingTable/query',
            method:'post',
            data
        })
    },

    queryById(data){
        return request({
            url:'/ComplianceTrainingTable/queryById',
            method:'post',
            data
        })
    },

    save(data){
        return request({
            url:'/ComplianceTrainingTable/save',
            method:'post',
            data
        })
    },

    upAndDown(data){
        return request({
            url:'/ComplianceTrainingTable/likeOrDislike',
            method:'post',
            data
        })
    },

    queryReport(data){
        return request({
            url:'/ComplianceTrainingReports/query',
            method:'post',
            data
        })
    },

    queryYh(data){
        return request({
            url:"/ComplianceTrainingHousehold/query",
            method:'post',
            data
        })
    },

    sureUpload(data){
        return request({
            url:'/ComplianceTrainingReports/save',
            method:'post',
            data
        })
    },

    // 获取学习进度
    queryStudyData(data){
        return request({
            url:'/ComplianceTrainingHousehold/queryLearningProcessDia',
            method:'post',
            data
        })
    },

    //获取评论接口
    queryReview(data){
        return request({
            url:'/ComplianceTrainingReviews/query',
            method:'post',
            data
        })
    },

    // 发表评论
    saveReview(data){
        return request({
            url:'/ComplianceTrainingReviews/save',
            method:'post',
            data
        })
    },

    //开始学习
    studyBegin(data){
        return request({
            url:'/ComplianceTrainingHousehold/updateLearningProcess',
            method:'post',
            data
        })
    },

    // 获取用户学习详情
    queryDetail(data){
        return request({
            url:'/ComplianceTrainingHousehold/queryById',
            method:'post',
            data
        })
    },

    changeStudy(data){
        return request({
            url:'/ComplianceTrainingHousehold/updateCourseProcess',
            method:'post',
            data
        })
    },
    //更新学习子表视频进度
    updateVideoProgress(data){
        return request({
            url:'/ComplianceTrainingHousehold/updateCourseVideoProgress',
            method:'post',
            data
        })
    }
}