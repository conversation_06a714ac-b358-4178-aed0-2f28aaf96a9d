import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/stampduty/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/stampduty/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/stampduty/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/stampduty/queryById',
      method: 'post',
      data
    })
  },
  checkOnly(data) {
    return request({
      url: '/stampduty/checkOnly',
      method: 'post',
      data
    })
  },
  queryOwnData(data) {
    return request({
      url: '/stampduty/queryOwnData',
      method: 'post',
      data
    })
  }
}