import {request} from '@/api/index'

export default {
    queryList(data){
        return request({
            url: '/projectDistriReFeedBack/queryList',
            method: 'post',
            data
        })
    },
    distribution(id,data){
        return request({
            url: '/projectDistriReFeedBack/distribution/' + id,
            method: 'post',
            data
        })
    },
    queryById(data){
        return request({
            url: '/projectDistriReFeedBack/queryById/' + data,
            method: 'get'
        })
    },
    saveSubmit(data){
        return request({
            url: '/projectDistriReFeedBack/saveSubmit',
            method: 'post',
            data
        })
    }
}