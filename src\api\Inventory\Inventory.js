import {request} from '@/api/index';

export default {
    // 添加商品库存信息
    addInventory(data) {
        return request({
            url: '/api/inventory/add',
            method: 'post',
            data
        })
    },

    // 更新商品库存信息
    updateInventoryInfo(data) {
        return request({
            url: '/api/inventory/updateInfo',
            method: 'put',
            data
        })
    },

    // 根据商品ID查询库存
    getInventoryByProductId(productId) {
        return request({
            url: '/api/inventory/getByProductId/' + productId,
            method: 'get'
        })
    },

    // 根据商品名称查询库存（支持模糊查询）
    getInventoryByProductName(productName) {
        return request({
            url: '/api/inventory/getByProductName',
            method: 'get',
            params: { productName }
        })
    },

    // 获取所有库存信息
    getAllInventory() {
        return request({
            url: '/api/inventory/getAll',
            method: 'get'
        })
    },

    // 更新商品库存（增加或减少）
    updateInventory(data) {
        return request({
            url: '/api/inventory/update',
            method: 'put',
            data: data
        })
    },

    // 查询低于预警阈值的商品库存
    checkWarningInventory() {
        return request({
            url: '/api/inventory/warning',
            method: 'get'
        })
    },

    // 检查指定商品库存是否充足
    checkStockSufficient(data) {
        return request({
            url: '/api/inventory/checkStock',
            method: 'get',
            params: data
        })
    },

    // 分页查询库存
    queryInventory(data) {
        return request({
            url: '/api/inventory/query',
            method: 'post',
            data
        })
    },

    // 批量更新库存
    batchUpdateInventory(data) {
        return request({
            url: '/api/inventory/batchUpdate',
            method: 'post',
            data
        })
    },

    // 删除库存信息
    deleteInventory(productId) {
        return request({
            url: '/api/inventory/delete/' + productId,
            method: 'delete'
        })
    },

    // 导出库存数据
    exportInventory(data) {
        return request({
            url: '/api/inventory/export',
            method: 'post',
            data,
            responseType: 'blob'
        })
    },

    // 库存历史记录
    getInventoryHistory(productId) {
        return request({
            url: '/api/inventory/history/' + productId,
            method: 'get'
        })
    },

    // 库存统计
    getInventoryStatistics() {
        return request({
            url: '/api/inventory/statistics',
            method: 'get'
        })
    }
} 