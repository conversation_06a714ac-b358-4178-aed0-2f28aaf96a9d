import { request } from '@/api/index'

export default {
  /**
   * 案件台账查询
   * @param data
   * @return {*}
   */
  queryCaseLeger(data) {
    return request({
      url: '/CaseLedger/queryCaseLeger',
      method: 'post',
      data
    })
  },

  /**
   * 案件台账左侧快速检索分类查询
   * @param data
   * @return {*}
   */
  queryLedgerFastQueryCase(data) {
    return request({
      url: '/CaseLedger/queryLedgerFastQueryCase',
      method: 'post',
      data
    })
  },

  /**
   * 案件台账导出
   * @param data
   * @return {*}
   */
  exportCaseLedger(data) {
    return request({
      url: '/CaseLedger/exportCaseLedger',
      method: 'post',
      responseType: 'blob',
      data
    })
  },

  /**
   * 重大案件一案一表导出
   */
  exportGreatCaseZip(data) {
    return request({
      url: '/CaseLedger/exportGreatCaseZip',
      method: 'post',
      responseType: 'blob',
      data
    })
  },




  queryFastQueryCase(data) {
    return request({
      url: '/CaseLedger/queryFastQueryCase',
      method: 'post',
      data
    })
  },
  queryFastQueryCase4(data) {
    return request({
      url: '/CaseLedger/queryFastQueryCase4',
      method: 'post',
      data
    })
  },
  queryFastQueryCase2(data) {
    return request({
      url: '/CaseLedger/queryFastQueryCase2',
      method: 'post',
      data
    })
  },
  queryFastQueryCase3(data) {
    return request({
      url: '/CaseLedger/queryFastQueryCase3',
      method: 'post',
      data
    })
  },
  queryTimeAxis(data) {
    return request({
      url: '/CaseLedger/queryTimeAxis',
      method: 'post',
      data
    })
  },
  queryCaseByID(data) {
    return request({
      url: '/CaseLedger/queryCaseByID',
      method: 'post',
      data
    })
  },
  queryCaseProcess(data) {
    return request({
      url: '/CaseLedger/queryCaseProcess',
      method: 'post',
      data
    })
  },
  queryDataByParentId(data) {
    return request({
      url: '/CaseLedger/queryDataByParentId',
      method: 'post',
      data
    })
  },
}
