<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>真实接口返回数据测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            margin: 10px 0;
            border-left: 4px solid #409eff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .contract-form {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }

        .success-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }

        .error-info {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>真实接口返回数据测试</h1>
            <p>测试修复后的岗位职责显示功能</p>
        </div>

        <div class="demo-section">
            <h3>📋 真实接口返回的currentNodes数据</h3>
            <div class="code-block">{{ JSON.stringify(realApiResponse.currentNodes, null, 2) }}</div>
            
            <h4>数据字段分析：</h4>
            <div class="debug-info">
                <strong>关键字段：</strong><br>
                - name: "{{ realApiResponse.currentNodes[0].name }}" (岗位名称)<br>
                - nameId: "{{ realApiResponse.currentNodes[0].nameId }}" (岗位ID)<br>
                - nodeId: "{{ realApiResponse.currentNodes[0].nodeId }}" (节点ID)
            </div>
            
            <div style="margin-top: 20px;">
                <el-button @click="testDataExtraction" type="primary">测试数据提取逻辑</el-button>
                <el-button @click="simulateGetCurrentNodeInfo" type="success">模拟getCurrentNodeInfo方法</el-button>
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="demo-section" v-if="testResults.length > 0">
            <h3>🔍 测试结果</h3>
            <div v-for="(result, index) in testResults" :key="index" 
                 :class="result.success ? 'success-info' : 'error-info'">
                <strong>{{ result.title }}:</strong><br>
                {{ result.message }}
            </div>
        </div>

        <!-- 模拟合同审批单界面 -->
        <div class="contract-form">
            <!-- 岗位职责显示行 -->
            <el-row v-if="currentNodeInfo" style="margin-top: 10px;">
                <el-col :span="24" style="text-align: center;">
                    <el-tag type="info" size="medium" style="padding: 8px 16px; font-size: 14px;">
                        <i class="el-icon-user-solid" style="margin-right: 5px;"></i>
                        当前岗位职责：{{ currentNodeInfo }}
                    </el-tag>
                </el-col>
            </el-row>
            
            <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">新增合同审批单</span>
            </el-row>

            <!-- 模拟表单内容 -->
            <div style="margin-top: 20px; padding: 20px; border: 1px solid #e9ecef; border-radius: 4px;">
                <p><strong>合同名称：</strong>{{ realApiResponse.data.contractName }}</p>
                <p><strong>合同编号：</strong>{{ realApiResponse.data.contractCode }}</p>
                <p><strong>合同类型：</strong>{{ realApiResponse.data.contractType }}</p>
                <p><strong>合同金额：</strong>{{ realApiResponse.data.contractMoney }}元</p>
                <p><strong>甲方：</strong>{{ realApiResponse.data.ourPartyName }}</p>
                <p><strong>乙方：</strong>{{ realApiResponse.data.otherPartyName }}</p>
                <p><strong>审批状态：</strong>{{ realApiResponse.data.dataState }}</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                currentNodeInfo: null,
                testResults: [],
                // 真实的接口返回数据
                realApiResponse: {
                    "currentNodes": [
                        {
                            "processInstanceId": "3305007",
                            "processDefinitionId": "JTHTSP:1:3305006",
                            "name": "业务部门负责人",
                            "nameId": "Activity_1rmp80v",
                            "startTime": "2025-08-26 08:32:50",
                            "processDefinitionName": "JTHTSP:1:3305006",
                            "assignee": "60019370",
                            "endTime": "2025-08-26 08:32:54",
                            "nodeId": "Activity_0u0241c",
                            "taskId": "3305049"
                        }
                    ],
                    "data": {
                        "contractName": "测试0826",
                        "contractCode": "A20000007BG02250800001",
                        "contractType": "仓储合同",
                        "contractMoney": 1111.00,
                        "ourPartyName": "内蒙古新联信息产业有限公司",
                        "otherPartyName": "包头市龙兴业路桥工程有限责任公司",
                        "dataState": "审批中"
                    }
                }
            },
            mounted() {
                // 页面加载时自动测试
                this.simulateGetCurrentNodeInfo();
            },
            methods: {
                // 根据nodeId获取岗位名称（复制修复后的逻辑）
                getNodeNameByNodeId(nodeId) {
                    const nodeMap = {
                        'Activity_1rmp80v': '业务部门负责人',
                        'Activity_1yc9eu3': '运改部审核',
                        'Activity_1upb5zy': '税务审核',
                        'Activity_0hgi73c': '资金审核',
                        'Activity_0no6qkt': '财务部部长',
                        'Activity_1qs8r6i': '法务部风控',
                        'Activity_1lee3nx': '办公室',
                        'Activity_1umzmjb': '公司领导',
                        'Activity_0wn3tir': '返回经办人',
                        'Activity_0y3xjh6': '法务承办人',
                        'Activity_1e2ebp6': '合同专业负责人',
                        'Activity_0pdswu8': '法务部部长',
                        'Activity_0pz4x4e': '首席合规官',
                        'Activity_1r1du0j': '三级审批',
                        'Activity_0u0241c': '三级审批'
                    };
                    
                    return nodeMap[nodeId] || `未知岗位(${nodeId})`;
                },
                
                // 测试数据提取逻辑
                testDataExtraction() {
                    this.testResults = [];
                    const currentNode = this.realApiResponse.currentNodes[0];
                    
                    // 测试1: 检查name字段
                    if (currentNode.name) {
                        this.testResults.push({
                            title: '测试1 - name字段提取',
                            message: `成功提取到name字段: "${currentNode.name}"`,
                            success: true
                        });
                    } else {
                        this.testResults.push({
                            title: '测试1 - name字段提取',
                            message: 'name字段为空或不存在',
                            success: false
                        });
                    }
                    
                    // 测试2: 检查nameId字段
                    if (currentNode.nameId) {
                        const mappedName = this.getNodeNameByNodeId(currentNode.nameId);
                        this.testResults.push({
                            title: '测试2 - nameId字段映射',
                            message: `nameId "${currentNode.nameId}" 映射为: "${mappedName}"`,
                            success: true
                        });
                    } else {
                        this.testResults.push({
                            title: '测试2 - nameId字段映射',
                            message: 'nameId字段为空或不存在',
                            success: false
                        });
                    }
                    
                    // 测试3: 检查nodeId字段
                    if (currentNode.nodeId) {
                        const mappedName = this.getNodeNameByNodeId(currentNode.nodeId);
                        this.testResults.push({
                            title: '测试3 - nodeId字段映射',
                            message: `nodeId "${currentNode.nodeId}" 映射为: "${mappedName}"`,
                            success: true
                        });
                    } else {
                        this.testResults.push({
                            title: '测试3 - nodeId字段映射',
                            message: 'nodeId字段为空或不存在',
                            success: false
                        });
                    }
                    
                    this.$message.success('数据提取测试完成');
                },
                
                // 模拟getCurrentNodeInfo方法（修复后的逻辑）
                simulateGetCurrentNodeInfo() {
                    this.testResults = [];
                    
                    // 模拟接口调用成功
                    const res = this.realApiResponse;
                    console.log('模拟接口返回数据:', res);
                    
                    if (res && res.currentNodes && res.currentNodes.length > 0) {
                        const currentNode = res.currentNodes[0];
                        console.log('当前节点信息:', currentNode);
                        
                        // 优先使用name字段，如果没有则使用nameId映射
                        if (currentNode.name) {
                            this.currentNodeInfo = currentNode.name;
                            this.testResults.push({
                                title: '岗位职责获取结果',
                                message: `成功使用name字段: "${this.currentNodeInfo}"`,
                                success: true
                            });
                        } else if (currentNode.nameId) {
                            this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nameId);
                            this.testResults.push({
                                title: '岗位职责获取结果',
                                message: `使用nameId映射: "${this.currentNodeInfo}"`,
                                success: true
                            });
                        } else if (currentNode.nodeId) {
                            this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nodeId);
                            this.testResults.push({
                                title: '岗位职责获取结果',
                                message: `使用nodeId映射: "${this.currentNodeInfo}"`,
                                success: true
                            });
                        } else {
                            this.testResults.push({
                                title: '岗位职责获取结果',
                                message: '无法获取岗位信息，所有字段都为空',
                                success: false
                            });
                        }
                        
                        console.log('设置的岗位职责信息:', this.currentNodeInfo);
                        
                        if (this.currentNodeInfo) {
                            this.$message.success(`岗位职责显示成功: ${this.currentNodeInfo}`);
                        } else {
                            this.$message.error('岗位职责获取失败');
                        }
                    } else {
                        this.testResults.push({
                            title: '接口数据检查',
                            message: '接口返回数据中没有currentNodes信息',
                            success: false
                        });
                        this.$message.error('接口返回数据格式错误');
                    }
                }
            }
        });
    </script>
</body>
</html>
