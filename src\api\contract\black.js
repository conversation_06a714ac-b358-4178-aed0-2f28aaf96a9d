import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/sgOppositeManageBlack/query',
      method: 'post',
      data
    })
  },
  queryBlack(data) {
    return request({
      url: '/sgOppositeManageBlack/queryBlack',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/sgOppositeManageBlack/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/sgOppositeManageBlack/delete',
      method: 'post',
      data
    })
  },
  unlock(data) {
    return request({
      url: '/sgOppositeManageBlack/unlock',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/sgOppositeManageBlack/queryDataById',
      method: 'post',
      data
    })
  },
  queryCloseById(data) {
    return request({
      url: '/sgOppositeManageBlack/queryCloseById',
      method: 'post',
      data
    })
  },
  queryUnit(data) {
    return request({
      url: '/oppositeogn/queryUnit',
      method: 'post',
      data
    })
  },
}