import {request} from '@/api/index'

export default {
    //业务领域【最下方左1】
    queryBusinessDomainData(data) {
        return request({
            url: '/complianceRiskIndex/getBusinessDomainData',
            method: 'post',
            data
        })
    },
    //风险数量统计
    queryRiskEeventCount(data) {
        return request({
            url: '/complianceRiskIndex/getRiskEeventCount',
            method: 'post',
            data
        })
    },
    //预警风险等级占比【最下方中1】
    queryYjRiskLevelData(data) {
        return request({
            url: '/complianceRiskIndex/getYjRiskLevelData',
            method: 'post',
            data
        })
    },
    //风险事件占比统计【最下方最右侧】
    queryRiskLevelData(data) {
        return request({
            url: '/complianceRiskIndex/getRiskLevelData',
            method: 'post',
            data
        })
    },
    //公司风险数据【中间，公司风险数据】
    queryCompanyRiskData(data) {
      return request({
          url: '/complianceRiskIndex/getCompanyRiskData',
          method: 'post',
          data
      })
    }
   


}